# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 4

[[package]]
name = "addr2line"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e4503c46a5c0c7844e948c9a4d6acd9f50cccb4de1c48eb9e291ea17470c678"
dependencies = [
 "gimli 0.29.0",
]

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "gimli 0.31.1",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"

[[package]]
name = "adler32"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aae1277d39aeec15cb388266ecc24b11c80469deae6067e17a1a7aa9e5c1f234"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "ghash",
 "subtle",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.16",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a15f179cd60c4584b8a8c596927aadc462e27f2ca70c04e0071964a73ba7a75"
dependencies = [
 "cfg-if",
 "const-random",
 "getrandom 0.3.3",
 "once_cell",
 "serde",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "aligned-vec"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc890384c8602f339876ded803c97ad529f3842aba97f6392b3dba0dd171769b"
dependencies = [
 "equator",
]

[[package]]
name = "alloc-no-stdlib"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc7bb162ec39d46ab1ca8c77bf72e890535becd1751bb45f64c597edb4c8c6b3"

[[package]]
name = "alloc-stdlib"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94fb8275041c72129eb51b7d0322c29b8387a0386127718b096429201a5d6ece"
dependencies = [
 "alloc-no-stdlib",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "ambient-authority"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9d4ee0d472d1cd2e28c97dfa124b3d8d992e10eb0a035f33f5d12e3a177ba3b"

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi",
]

[[package]]
name = "anstream"
version = "0.6.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8acc5369981196006228e28809f761875c0327210a891e941f4c683b3a99529b"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon",
 "colorchoice",
 "is_terminal_polyfill",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cc3b69f167a1ef2e161439aa98aed94e6028e5f9a59be9a6ffb47aef1651f9"

[[package]]
name = "anstyle-parse"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2d16507662817a6a20a9ea92df6652ee4f94f914589377d69f3b21bc5798a9"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79947af37f4177cfead1110013d678905c37501914fba0efea834c3fe9a8d60c"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3534e77181a9cc07539ad51f2141fe32f6c3ffd4df76db8ad92346b003ae4e"
dependencies = [
 "anstyle",
 "once_cell",
 "windows-sys 0.59.0",
]

[[package]]
name = "anyerror"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71add24cc141a1e8326f249b74c41cfd217aeb2a67c9c6cf9134d175469afd49"
dependencies = [
 "serde",
]

[[package]]
name = "anyhow"
version = "1.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e16d2d3311acee920a9eb8d33b8cbc1787ce4a264e85f964c2404b969bdcd487"
dependencies = [
 "backtrace",
]

[[package]]
name = "apache-avro"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aef82843a0ec9f8b19567445ad2421ceeb1d711514384bdd3d49fe37102ee13"
dependencies = [
 "bigdecimal",
 "bzip2 0.4.4",
 "crc32fast",
 "digest",
 "libflate",
 "log",
 "num-bigint",
 "quad-rand",
 "rand 0.8.5",
 "regex-lite",
 "serde",
 "serde_bytes",
 "serde_json",
 "snap",
 "strum 0.26.3",
 "strum_macros 0.26.4",
 "thiserror 1.0.69",
 "typed-builder 0.19.1",
 "uuid",
 "xz2",
 "zstd 0.13.3",
]

[[package]]
name = "approx"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cab112f0a86d568ea0e627cc1d6be74a1e9cd55214684db5561995f6dad897c6"
dependencies = [
 "num-traits",
]

[[package]]
name = "arbitrary"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dde20b3d026af13f561bdd0f15edf01fc734f0dafcedbaf42bba506a9517f223"
dependencies = [
 "derive_arbitrary",
]

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"

[[package]]
name = "ariadne"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44055e597c674aef7cb903b2b9f6e4cba1277ed0d2d61dae7cd52d7ffa81f8e2"
dependencies = [
 "unicode-width 0.1.14",
 "yansi",
]

[[package]]
name = "array-init"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d62b7694a562cdf5a74227903507c56ab2cc8bdd1f781ed5cb4cf9c9f810bfc"

[[package]]
name = "arrayref"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76a2e8124351fda1ef8aaaa3bbd7ebbcb486bbcd4225aca0aa0d84bb2db8fecb"

[[package]]
name = "arrayvec"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c02d123df017efcdfbd739ef81735b36c5ba83ec3c59c80a9d7ecc718f92e50"

[[package]]
name = "arrow"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1bb018b6960c87fd9d025009820406f74e83281185a8bdcb44880d2aa5c9a87"
dependencies = [
 "arrow-arith",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-csv",
 "arrow-data",
 "arrow-ipc",
 "arrow-json",
 "arrow-ord",
 "arrow-row",
 "arrow-schema",
 "arrow-select",
 "arrow-string",
 "pyo3",
]

[[package]]
name = "arrow-arith"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44de76b51473aa888ecd6ad93ceb262fb8d40d1f1154a4df2f069b3590aa7575"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "chrono",
 "num",
]

[[package]]
name = "arrow-array"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29ed77e22744475a9a53d00026cf8e166fe73cf42d89c4c4ae63607ee1cfcc3f"
dependencies = [
 "ahash 0.8.12",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "chrono",
 "chrono-tz 0.10.3",
 "half",
 "hashbrown 0.15.3",
 "num",
]

[[package]]
name = "arrow-buffer"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0391c96eb58bf7389171d1e103112d3fc3e5625ca6b372d606f2688f1ea4cce"
dependencies = [
 "bytes",
 "half",
 "num",
]

[[package]]
name = "arrow-cast"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f39e1d774ece9292697fcbe06b5584401b26bd34be1bec25c33edae65c2420ff"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "arrow-select",
 "atoi",
 "base64 0.22.1",
 "chrono",
 "comfy-table",
 "half",
 "lexical-core",
 "num",
 "ryu",
]

[[package]]
name = "arrow-csv"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9055c972a07bf12c2a827debfd34f88d3b93da1941d36e1d9fee85eebe38a12a"
dependencies = [
 "arrow-array",
 "arrow-cast",
 "arrow-schema",
 "chrono",
 "csv",
 "csv-core",
 "lazy_static",
 "regex",
]

[[package]]
name = "arrow-data"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf75ac27a08c7f48b88e5c923f267e980f27070147ab74615ad85b5c5f90473d"
dependencies = [
 "arrow-buffer",
 "arrow-schema",
 "half",
 "num",
]

[[package]]
name = "arrow-flight"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91efc67a4f5a438833dd76ef674745c80f6f6b9a428a3b440cbfbf74e32867e6"
dependencies = [
 "arrow-arith",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-data",
 "arrow-ipc",
 "arrow-ord",
 "arrow-row",
 "arrow-schema",
 "arrow-select",
 "arrow-string",
 "base64 0.22.1",
 "bytes",
 "futures",
 "once_cell",
 "paste",
 "prost",
 "prost-types",
 "tonic",
]

[[package]]
name = "arrow-ipc"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a222f0d93772bd058d1268f4c28ea421a603d66f7979479048c429292fac7b2e"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "flatbuffers",
 "lz4_flex",
 "zstd 0.13.3",
]

[[package]]
name = "arrow-json"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9085342bbca0f75e8cb70513c0807cc7351f1fbf5cb98192a67d5e3044acb033"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-data",
 "arrow-schema",
 "chrono",
 "half",
 "indexmap 2.9.0",
 "lexical-core",
 "memchr",
 "num",
 "serde",
 "serde_json",
 "simdutf8",
]

[[package]]
name = "arrow-ord"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab2f1065a5cad7b9efa9e22ce5747ce826aa3855766755d4904535123ef431e7"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "arrow-select",
]

[[package]]
name = "arrow-row"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3703a0e3e92d23c3f756df73d2dc9476873f873a76ae63ef9d3de17fda83b2d8"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "half",
]

[[package]]
name = "arrow-schema"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73a47aa0c771b5381de2b7f16998d351a6f4eb839f1e13d48353e17e873d969b"
dependencies = [
 "bitflags 2.9.0",
 "serde",
]

[[package]]
name = "arrow-select"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24b7b85575702b23b85272b01bc1c25a01c9b9852305e5d0078c79ba25d995d4"
dependencies = [
 "ahash 0.8.12",
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "num",
]

[[package]]
name = "arrow-string"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9260fddf1cdf2799ace2b4c2fc0356a9789fa7551e0953e35435536fecefebbd"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "arrow-select",
 "memchr",
 "num",
 "regex",
 "regex-syntax 0.8.5",
]

[[package]]
name = "arrow-udf-runtime"
version = "0.8.0"
source = "git+https://github.com/datafuse-extras/arrow-udf.git?rev=a442343#a44234332e9c182c247a510c3721b655572f323c"
dependencies = [
 "anyhow",
 "arrow-array",
 "arrow-buffer",
 "arrow-ipc",
 "arrow-schema",
 "arrow-select",
 "async-trait",
 "atomic-time",
 "base64 0.22.1",
 "futures-util",
 "genawaiter2",
 "itertools 0.14.0",
 "pyo3",
 "pyo3-build-config",
 "reqwest",
 "rquickjs",
 "serde_json",
 "tempfile",
 "thiserror 2.0.12",
 "tokio",
 "wasi-common",
 "wasmtime",
]

[[package]]
name = "assert-json-diff"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47e4f2b81832e72834d7518d8487a0396a28cc408186a2e8854c0f98011faf12"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "async-backtrace"
version = "0.2.6"
source = "git+https://github.com/datafuse-extras/async-backtrace.git?rev=dea4553#dea4553c47ff2946684f7efb295a4105d5627fac"
dependencies = [
 "async-backtrace-attributes",
 "dashmap 5.5.3",
 "futures",
 "itertools 0.10.5",
 "loom 0.5.6",
 "once_cell",
 "pin-project-lite",
 "rustc-hash 1.1.0",
 "static_assertions",
]

[[package]]
name = "async-backtrace-attributes"
version = "0.2.6"
source = "git+https://github.com/datafuse-extras/async-backtrace.git?rev=dea4553#dea4553c47ff2946684f7efb295a4105d5627fac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-broadcast"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "435a87a52755b8f27fcf321ac4f04b2802e337c8c4872923137471ec39c37532"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-channel"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81953c529336010edd6d8e358f886d9581267795c61b19475b71314bffa46d35"
dependencies = [
 "concurrent-queue",
 "event-listener 2.5.3",
 "futures-core",
]

[[package]]
name = "async-channel"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89b47800b0be77592da0afd425cc03468052844aff33b84e33cc696f64e77b6a"
dependencies = [
 "concurrent-queue",
 "event-listener-strategy",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-compat"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bab94bde396a3f7b4962e396fdad640e241ed797d4d8d77fc8c237d14c58fc0"
dependencies = [
 "futures-core",
 "futures-io",
 "once_cell",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "async-compression"
version = "0.4.3"
source = "git+https://github.com/datafuse-extras/async-compression?rev=dc81082#dc8108229e3a0288ee9097c986b89a90788fe9f6"
dependencies = [
 "brotli 3.5.0",
 "bzip2 0.4.4",
 "deflate64",
 "flate2",
 "futures-core",
 "futures-io",
 "memchr",
 "pin-project-lite",
 "xz2",
 "zstd 0.12.4",
 "zstd-safe 6.0.6",
]

[[package]]
name = "async-compression"
version = "0.4.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b37fc50485c4f3f736a4fb14199f6d5f5ba008d7f28fe710306c92780f004c07"
dependencies = [
 "brotli 8.0.1",
 "flate2",
 "futures-core",
 "memchr",
 "pin-project-lite",
 "tokio",
 "zstd 0.13.3",
 "zstd-safe 7.2.4",
]

[[package]]
name = "async-lock"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff6e472cdea888a4bd64f342f09b3f50e1886d32afe8df3d663c01140b811b18"
dependencies = [
 "event-listener 5.4.0",
 "event-listener-strategy",
 "pin-project-lite",
]

[[package]]
name = "async-recursion"
version = "1.1.1"
source = "git+https://github.com/datafuse-extras/async-recursion.git?rev=a353334#a353334ceb408c80a802634ff0cfeeb7a50d0ef7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-stream"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b5a71a6f37880a80d1d7f19efd781e4b5de42c88f0722cc13bcb6cc2cfe8476"
dependencies = [
 "async-stream-impl",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-stream-impl"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7c24de15d275a1ecfd47a380fb4d5ec9bfe0933f309ed5e705b775596a3574d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "async-task"
version = "4.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b75356056920673b02621b35afd0f7dda9306d03c79a30f5c56c44cf256e3de"

[[package]]
name = "async-trait"
version = "0.1.88"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e539d3fca749fcee5236ab05e93a52867dd549cc157c8cb7f99595f3cedffdb5"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "atoi"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f28d99ec8bfea296261ca1af174f24225171fea9664ba9003cbebee704810528"
dependencies = [
 "num-traits",
]

[[package]]
name = "atomic-time"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9622f5c6fb50377516c70f65159e70b25465409760c6bd6d4e581318bf704e83"
dependencies = [
 "once_cell",
 "portable-atomic",
]

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "atty"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9b39be18770d11421cdb1b9947a45dd3f37e93092cbf377614828a319d5fee8"
dependencies = [
 "hermit-abi 0.1.19",
 "libc",
 "winapi",
]

[[package]]
name = "auto_ops"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7460f7dd8e100147b82a63afca1a20eb6c231ee36b90ba7272e14951cb58af59"

[[package]]
name = "autocfg"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ace50bade8e6234aa140d9a2f552bbee1db4d353f69b8217bc503490fc1a9f26"

[[package]]
name = "aws-config"
version = "1.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6fcc63c9860579e4cb396239570e979376e70aab79e496621748a09913f8b36"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-sdk-sso",
 "aws-sdk-ssooidc",
 "aws-sdk-sts",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "hex",
 "http 1.3.1",
 "ring",
 "time",
 "tokio",
 "tracing",
 "url",
 "zeroize",
]

[[package]]
name = "aws-credential-types"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "687bc16bc431a8533fe0097c7f0182874767f920989d7260950172ae8e3c4465"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "zeroize",
]

[[package]]
name = "aws-lc-rs"
version = "1.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fcc8f365936c834db5514fc45aee5b1202d677e6b40e48468aaaa8183ca8c7"
dependencies = [
 "aws-lc-sys",
 "zeroize",
]

[[package]]
name = "aws-lc-sys"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61b1d86e7705efe1be1b569bab41d4fa1e14e220b60a160f78de2db687add079"
dependencies = [
 "bindgen 0.69.5",
 "cc",
 "cmake",
 "dunce",
 "fs_extra",
]

[[package]]
name = "aws-runtime"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c4063282c69991e57faab9e5cb21ae557e59f5b0fb285c196335243df8dc25c"
dependencies = [
 "aws-credential-types",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "http-body 0.4.6",
 "percent-encoding",
 "pin-project-lite",
 "tracing",
 "uuid",
]

[[package]]
name = "aws-sdk-glue"
version = "1.93.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccb2d0d52071ea998c48ec8120a693e98024d410aebc494de4a126b4ab0347a5"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-s3tables"
version = "1.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "659216f4201b777f7608fb4c98812f28abf5d66c42094a8bc0301a372444e38a"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-sso"
version = "1.67.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d4863da26489d1e6da91d7e12b10c17e86c14f94c53f416bd10e0a9c34057ba"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-ssooidc"
version = "1.68.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95caa3998d7237789b57b95a8e031f60537adab21fa84c91e35bef9455c652e4"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-sts"
version = "1.68.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4939f6f449a37308a78c5a910fd91265479bd2bb11d186f0b8fc114d89ec828d"
dependencies = [
 "aws-credential-types",
 "aws-runtime",
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-json",
 "aws-smithy-query",
 "aws-smithy-runtime",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "fastrand",
 "http 0.2.12",
 "once_cell",
 "regex-lite",
 "tracing",
]

[[package]]
name = "aws-sigv4"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3503af839bd8751d0bdc5a46b9cac93a003a353e635b0c12cf2376b5b53e41ea"
dependencies = [
 "aws-credential-types",
 "aws-smithy-http",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes",
 "form_urlencoded",
 "hex",
 "hmac",
 "http 0.2.12",
 "http 1.3.1",
 "percent-encoding",
 "sha2",
 "time",
 "tracing",
]

[[package]]
name = "aws-smithy-async"
version = "1.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e190749ea56f8c42bf15dd76c65e14f8f765233e6df9b0506d9d934ebef867c"
dependencies = [
 "futures-util",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "aws-smithy-http"
version = "0.62.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99335bec6cdc50a346fda1437f9fefe33abf8c99060739a546a16457f2862ca9"
dependencies = [
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes",
 "bytes-utils",
 "futures-core",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "percent-encoding",
 "pin-project-lite",
 "pin-utils",
 "tracing",
]

[[package]]
name = "aws-smithy-http-client"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e44697a9bded898dcd0b1cb997430d949b87f4f8940d91023ae9062bf218250"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "h2 0.4.10",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "hyper 0.14.32",
 "hyper 1.6.0",
 "hyper-rustls 0.24.2",
 "hyper-rustls 0.27.5",
 "hyper-util",
 "pin-project-lite",
 "rustls 0.21.12",
 "rustls 0.23.27",
 "rustls-native-certs 0.8.1",
 "rustls-pki-types",
 "tokio",
 "tower 0.5.2",
 "tracing",
]

[[package]]
name = "aws-smithy-json"
version = "0.61.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92144e45819cae7dc62af23eac5a038a58aa544432d2102609654376a900bd07"
dependencies = [
 "aws-smithy-types",
]

[[package]]
name = "aws-smithy-observability"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9364d5989ac4dd918e5cc4c4bdcc61c9be17dcd2586ea7f69e348fc7c6cab393"
dependencies = [
 "aws-smithy-runtime-api",
]

[[package]]
name = "aws-smithy-query"
version = "0.60.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2fbd61ceb3fe8a1cb7352e42689cec5335833cd9f94103a61e98f9bb61c64bb"
dependencies = [
 "aws-smithy-types",
 "urlencoding",
]

[[package]]
name = "aws-smithy-runtime"
version = "1.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14302f06d1d5b7d333fd819943075b13d27c7700b414f574c3c35859bfb55d5e"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-http-client",
 "aws-smithy-observability",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "bytes",
 "fastrand",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "http-body 1.0.1",
 "pin-project-lite",
 "pin-utils",
 "tokio",
 "tracing",
]

[[package]]
name = "aws-smithy-runtime-api"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1e5d9e3a80a18afa109391fb5ad09c3daf887b516c6fd805a157c6ea7994a57"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-types",
 "bytes",
 "http 0.2.12",
 "http 1.3.1",
 "pin-project-lite",
 "tokio",
 "tracing",
 "zeroize",
]

[[package]]
name = "aws-smithy-types"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40076bd09fadbc12d5e026ae080d0930defa606856186e31d83ccc6a255eeaf3"
dependencies = [
 "base64-simd",
 "bytes",
 "bytes-utils",
 "futures-core",
 "http 0.2.12",
 "http 1.3.1",
 "http-body 0.4.6",
 "http-body 1.0.1",
 "http-body-util",
 "itoa",
 "num-integer",
 "pin-project-lite",
 "pin-utils",
 "ryu",
 "serde",
 "time",
 "tokio",
 "tokio-util",
]

[[package]]
name = "aws-smithy-xml"
version = "0.60.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab0b0166827aa700d3dc519f72f8b3a91c35d0b8d042dc5d643a91e6f80648fc"
dependencies = [
 "xmlparser",
]

[[package]]
name = "aws-types"
version = "1.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a322fec39e4df22777ed3ad8ea868ac2f94cd15e1a55f6ee8d8d6305057689a"
dependencies = [
 "aws-credential-types",
 "aws-smithy-async",
 "aws-smithy-runtime-api",
 "aws-smithy-types",
 "rustc_version",
 "tracing",
]

[[package]]
name = "axum"
version = "0.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edca88bc138befd0323b20752846e6587272d3b03b0343c8ea28a6f819e6e71f"
dependencies = [
 "async-trait",
 "axum-core",
 "bytes",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "itoa",
 "matchit",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "rustversion",
 "serde",
 "sync_wrapper",
 "tower 0.5.2",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09f2bd6146b97ae3359fa0cc6d6b376d9539582c7b4220f041a33ec24c226199"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "mime",
 "pin-project-lite",
 "rustversion",
 "sync_wrapper",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "backoff"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62ddb9cb1ec0a098ad4bbf9344d0713fa193ae1a80af55febcff2627b6a00c1"
dependencies = [
 "futures-core",
 "getrandom 0.2.16",
 "instant",
 "pin-project-lite",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "backon"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd0b50b1b78dbadd44ab18b3c794e496f3a139abb9fbc27d9c94c4eebbb96496"
dependencies = [
 "fastrand",
 "gloo-timers",
 "tokio",
]

[[package]]
name = "backtrace"
version = "0.3.73"
source = "git+https://github.com/rust-lang/backtrace-rs.git?rev=72265be#72265bea210891ae47bbe6d4f17b493ef0606619"
dependencies = [
 "addr2line 0.22.0",
 "cc",
 "cfg-if",
 "libc",
 "miniz_oxide 0.7.4",
 "object",
 "rustc-demangle",
 "serde",
]

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b3254f16251a8381aa12e40e3c4d2f0199f8c6508fbecb9d91f575e0fbb8c6"

[[package]]
name = "base64-simd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339abbe78e73178762e23bea9dfd08e697eb3f3301cd4be981c0f78ba5859195"
dependencies = [
 "outref",
 "vsimd",
]

[[package]]
name = "base64ct"
version = "1.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89e25b6adfb930f02d1981565a6e5d9c547ac15a96606256d3b59040e5cd4ca3"

[[package]]
name = "beef"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a8241f3ebb85c056b509d4327ad0358fbbba6ffb340bf388f26350aeda225b1"

[[package]]
name = "bendpy"
version = "0.1.0"
dependencies = [
 "arrow",
 "arrow-schema",
 "ctor",
 "databend-common-catalog",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-license",
 "databend-common-meta-app",
 "databend-common-users",
 "databend-query",
 "pyo3",
 "pyo3-build-config",
 "tempfile",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "bigdecimal"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a22f228ab7a1b23027ccc6c350b72868017af7ea8356fbdf19f8d991c690013"
dependencies = [
 "autocfg",
 "libm",
 "num-bigint",
 "num-integer",
 "num-traits",
 "serde",
]

[[package]]
name = "bimap"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "230c5f1ca6a325a32553f8640d31ac9b49f2411e901e427570154868b46da4f7"

[[package]]
name = "binary-heap-plus"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4551d8382e911ecc0d0f0ffb602777988669be09447d536ff4388d1def11296"
dependencies = [
 "compare",
]

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bincode"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36eaf5d7b090263e8150820482d5d93cd964a81e4019913c972f4edcc6edb740"
dependencies = [
 "bincode_derive",
 "serde",
 "unty",
]

[[package]]
name = "bincode_derive"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf95709a440f45e986983918d0e8a1f30a9b1df04918fc828670606804ac3c09"
dependencies = [
 "virtue",
]

[[package]]
name = "bindgen"
version = "0.69.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271383c67ccabffb7381723dea0672a673f292304fcb45c01cc648c7a8d58088"
dependencies = [
 "bitflags 2.9.0",
 "cexpr",
 "clang-sys",
 "itertools 0.12.1",
 "lazy_static",
 "lazycell",
 "log",
 "prettyplease",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 1.1.0",
 "shlex",
 "syn 2.0.101",
 "which",
]

[[package]]
name = "bindgen"
version = "0.71.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f58bf3d7db68cfbac37cfc485a8d711e87e064c3d0fe0435b92f7a407f9d6b3"
dependencies = [
 "bitflags 2.9.0",
 "cexpr",
 "clang-sys",
 "itertools 0.13.0",
 "proc-macro2",
 "quote",
 "regex",
 "rustc-hash 2.1.1",
 "shlex",
 "syn 2.0.101",
]

[[package]]
name = "binstring"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a3c2603413428303761fae99d4b6d936404208221a44eba47d7c1e6dd03a3"

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c8214115b7bf84099f1309324e63141d4c5d7cc26862f97a0a857dbefe165bd"
dependencies = [
 "serde",
]

[[package]]
name = "bitmaps"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "031043d04099746d8db04daf1fa424b2bc8bd69d92b25962dcde24da39ab64a2"
dependencies = [
 "typenum",
]

[[package]]
name = "bitpacking"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8c7d2ac73c167c06af4a5f37e6e59d84148d57ccbe4480b76f0273eefea82d7"
dependencies = [
 "crunchy",
]

[[package]]
name = "bitpacking"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c1d3e2bfd8d06048a179f7b17afc3188effa10385e7b00dc65af6aae732ea92"
dependencies = [
 "crunchy",
]

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty",
 "radium",
 "tap",
 "wyz",
]

[[package]]
name = "blake2b_simd"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06e903a20b159e944f91ec8499fe1e55651480c541ea0a584f5d967c49ad9d99"
dependencies = [
 "arrayref",
 "arrayvec",
 "constant_time_eq",
]

[[package]]
name = "blake3"
version = "1.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3888aaa89e4b2a40fca9848e400f6a658a5a3978de7be858e209cafa8be9a4a0"
dependencies = [
 "arrayref",
 "arrayvec",
 "cc",
 "cfg-if",
 "constant_time_eq",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array",
]

[[package]]
name = "block-padding"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8894febbff9f758034a5b8e12d87918f56dfc64a8e1fe757d65e29041538d93"
dependencies = [
 "generic-array",
]

[[package]]
name = "blocking"
version = "1.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "703f41c54fc768e63e091340b424302bb1c29ef4aa0c7f10fe849dfb114d29ea"
dependencies = [
 "async-channel 2.3.1",
 "async-task",
 "futures-io",
 "futures-lite",
 "piper",
]

[[package]]
name = "bollard"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d41711ad46fda47cd701f6908e59d1bd6b9a2b7464c0d0aeab95c6d37096ff8a"
dependencies = [
 "base64 0.22.1",
 "bollard-stubs",
 "bytes",
 "futures-core",
 "futures-util",
 "hex",
 "home",
 "http 1.3.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-named-pipe",
 "hyper-rustls 0.27.5",
 "hyper-util",
 "hyperlocal",
 "log",
 "pin-project-lite",
 "rustls 0.23.27",
 "rustls-native-certs 0.7.3",
 "rustls-pemfile 2.2.0",
 "rustls-pki-types",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_repr",
 "serde_urlencoded",
 "thiserror 1.0.69",
 "tokio",
 "tokio-util",
 "tower-service",
 "url",
 "winapi",
]

[[package]]
name = "bollard-stubs"
version = "1.45.0-rc.26.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d7c5415e3a6bc6d3e99eff6268e488fd4ee25e7b28c10f08fa6760bd9de16e4"
dependencies = [
 "serde",
 "serde_repr",
 "serde_with",
]

[[package]]
name = "borsh"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad8646f98db542e39fc66e68a20b2144f6a732636df7c2354e74645faaa433ce"
dependencies = [
 "borsh-derive",
 "cfg_aliases 0.2.1",
]

[[package]]
name = "borsh-derive"
version = "1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdd1d3c0c2f5833f22386f252fe8ed005c7f59fdcddeef025c01b4c3b9fd9ac3"
dependencies = [
 "once_cell",
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "brotli"
version = "3.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d640d25bc63c50fb1f0b545ffd80207d2e10a4c965530809b40ba3386825c391"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
 "brotli-decompressor 2.5.1",
]

[[package]]
name = "brotli"
version = "8.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9991eea70ea4f293524138648e41ee89b0b2b12ddef3b255effa43c8056e0e0d"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
 "brotli-decompressor 5.0.0",
]

[[package]]
name = "brotli-decompressor"
version = "2.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e2e4afe60d7dd600fdd3de8d0f08c2b7ec039712e3b6137ff98b7004e82de4f"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
]

[[package]]
name = "brotli-decompressor"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "874bb8112abecc98cbd6d81ea4fa7e94fb9449648c93cc89aa40c81c24d7de03"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
]

[[package]]
name = "bstr"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234113d19d0d7d613b40e86fb654acf958910802bcceab913a4f9e7cda03b1a4"
dependencies = [
 "memchr",
 "regex-automata 0.4.9",
 "serde",
]

[[package]]
name = "btoi"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd6407f73a9b8b6162d8a2ef999fe6afd7cc15902ebf42c5cd296addf17e0ad"
dependencies = [
 "num-traits",
]

[[package]]
name = "buf-list"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f56bd1685d994a3e2a3ed802eb1ecee8cb500b0ad4df48cb4d5d1a2f04749c3a"
dependencies = [
 "bytes",
]

[[package]]
name = "bumpalo"
version = "3.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1628fb46dfa0b37568d12e5edd512553eccf6a22a78e8bde00bb4aed84d5bdbf"

[[package]]
name = "byte-unit"
version = "5.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cd29c3c585209b0cbc7309bfe3ed7efd8c84c21b7af29c8bfae908f8777174"
dependencies = [
 "rust_decimal",
 "serde",
 "utf8-width",
]

[[package]]
name = "bytecheck"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23cdc57ce23ac53c931e88a43d06d070a6fd142f2617be5855eb75efc9beb1c2"
dependencies = [
 "bytecheck_derive",
 "ptr_meta 0.1.4",
 "simdutf8",
]

[[package]]
name = "bytecheck_derive"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db406d29fbcd95542e92559bed4d8ad92636d1ca8b3b72ede10b4bcc010e659"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "bytemuck"
version = "1.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9134a6ef01ce4b366b50689c94f82c14bc72bc5d0386829828a2e2752ef7958c"
dependencies = [
 "bytemuck_derive",
]

[[package]]
name = "bytemuck_derive"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ecc273b49b3205b83d648f0690daa588925572cc5063745bfe547fe7ec8e1a1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71b6127be86fdcfddb610f7182ac57211d4b18a3e9c82eb2d17662f2227ad6a"
dependencies = [
 "serde",
]

[[package]]
name = "bytes-utils"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dafe3a8757b027e2be6e4e5601ed563c55989fcf1546e933c66c8eb3a058d35"
dependencies = [
 "bytes",
 "either",
]

[[package]]
name = "bytesize"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e93abca9e28e0a1b9877922aacb20576e05d4679ffa78c3d6dc22a26a216659"

[[package]]
name = "bzip2"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdb116a6ef3f6c3698828873ad02c3014b3c85cadb88496095628e3ef1e347f8"
dependencies = [
 "bzip2-sys",
 "libc",
]

[[package]]
name = "bzip2"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49ecfb22d906f800d4fe833b6282cf4dc1c298f5057ca0b5445e5c209735ca47"
dependencies = [
 "bzip2-sys",
]

[[package]]
name = "bzip2-sys"
version = "0.1.13+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "225bff33b2141874fe80d71e07d6eec4f85c5c216453dd96388240f96e1acc14"
dependencies = [
 "cc",
 "pkg-config",
]

[[package]]
name = "camino"
version = "1.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b96ec4966b5813e2c0507c1f86115c8c5abaadc3980879c3424042a02fd1ad3"
dependencies = [
 "serde",
]

[[package]]
name = "cap-fs-ext"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e41cc18551193fe8fa6f15c1e3c799bc5ec9e2cfbfaa8ed46f37013e3e6c173c"
dependencies = [
 "cap-primitives",
 "cap-std",
 "io-lifetimes",
 "windows-sys 0.59.0",
]

[[package]]
name = "cap-primitives"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a1e394ed14f39f8bc26f59d4c0c010dbe7f0a1b9bafff451b1f98b67c8af62a"
dependencies = [
 "ambient-authority",
 "fs-set-times",
 "io-extras",
 "io-lifetimes",
 "ipnet",
 "maybe-owned",
 "rustix 1.0.7",
 "rustix-linux-procfs",
 "windows-sys 0.59.0",
 "winx",
]

[[package]]
name = "cap-rand"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0acb89ccf798a28683f00089d0630dfaceec087234eae0d308c05ddeaa941b40"
dependencies = [
 "ambient-authority",
 "rand 0.8.5",
]

[[package]]
name = "cap-std"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07c0355ca583dd58f176c3c12489d684163861ede3c9efa6fd8bba314c984189"
dependencies = [
 "cap-primitives",
 "io-extras",
 "io-lifetimes",
 "rustix 1.0.7",
]

[[package]]
name = "cap-time-ext"
version = "3.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "491af520b8770085daa0466978c75db90368c71896523f2464214e38359b1a5b"
dependencies = [
 "ambient-authority",
 "cap-primitives",
 "iana-time-zone",
 "once_cell",
 "rustix 1.0.7",
 "winx",
]

[[package]]
name = "cargo-license"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f061c0dbae23c1fe7ace394590d59b7e9bd24c7c678cd1b5f179081592d5894b"
dependencies = [
 "ansi_term",
 "anyhow",
 "cargo_metadata",
 "clap",
 "csv",
 "getopts",
 "itertools 0.12.1",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "spdx",
 "toml 0.8.22",
]

[[package]]
name = "cargo-platform"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35af189006b9c0f00a064685c727031e3ed2d8020f7ba284d78cc2671bd36ea"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d886547e41f740c616ae73108f6eb70afe6d940c7bc697cb30f13daec073037"
dependencies = [
 "camino",
 "cargo-platform",
 "semver",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "cbc"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b52a9543ae338f279b96b0b9fed9c8093744685043739079ce85cd58f289a6"
dependencies = [
 "cipher",
]

[[package]]
name = "cbordata"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d1b01dea75baf7521dfb06c2462d8339d71f4320a2244d1f8f6b94265ded858"
dependencies = [
 "cbordata-derive",
 "num-bigint",
 "num-traits",
]

[[package]]
name = "cbordata-derive"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9fd16dfe515476789addde9311da025a1f0c45ddd81b7b0315c9d426577dcb"
dependencies = [
 "heck 0.3.3",
 "lazy_static",
 "proc-macro-error 0.4.12",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "cc"
version = "1.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32db95edf998450acc7881c932f94cd9b05c87b4b2599e8bab064753da4acfd1"
dependencies = [
 "jobserver",
 "libc",
 "shlex",
]

[[package]]
name = "cedarwood"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d910bedd62c24733263d0bed247460853c9d22e8956bd4cd964302095e04e90"
dependencies = [
 "smallvec",
]

[[package]]
name = "census"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f4c707c6a209cbe82d10abd08e1ea8995e9ea937d2550646e02798948992be0"

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "cfg_aliases"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd16c4719339c4530435d38e511904438d07cce7950afa3718a84ac36c10e89e"

[[package]]
name = "cfg_aliases"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613afe47fcd5fac7ccf1db93babcb082c5994d996f20b8b159f2ad1658eb5724"

[[package]]
name = "chrono"
version = "0.4.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c469d952047f47f91b68d1cba3f10d63c11d73e4636f24f08daf0278abf01c4d"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "wasm-bindgen",
 "windows-link",
]

[[package]]
name = "chrono-tz"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d59ae0466b83e838b81a54256c39d5d7c20b9d7daa10510a242d9b75abd5936e"
dependencies = [
 "chrono",
 "chrono-tz-build 0.2.1",
 "phf",
 "serde",
]

[[package]]
name = "chrono-tz"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efdce149c370f133a071ca8ef6ea340b7b88748ab0810097a9e2976eaa34b4f3"
dependencies = [
 "chrono",
 "chrono-tz-build 0.4.1",
 "phf",
]

[[package]]
name = "chrono-tz-build"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "433e39f13c9a060046954e0592a8d0a4bcb1040125cbf91cb8ee58964cfb350f"
dependencies = [
 "parse-zoneinfo",
 "phf",
 "phf_codegen",
]

[[package]]
name = "chrono-tz-build"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f10f8c9340e31fc120ff885fcdb54a0b48e474bbd77cab557f0c30a3e569402"
dependencies = [
 "parse-zoneinfo",
 "phf_codegen",
]

[[package]]
name = "chumsky"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8eebd66744a15ded14960ab4ccdbfb51ad3b81f51f3f04a80adac98c985396c9"
dependencies = [
 "hashbrown 0.14.5",
 "stacker",
]

[[package]]
name = "cidr"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd1b64030216239a2e7c364b13cd96a2097ebf0dfe5025f2dedee14a23f2ab60"

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
]

[[package]]
name = "clang-sys"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b023947811758c97c59bf9d1c188fd619ad4718dcaa767947df1cadb14f39f4"
dependencies = [
 "glob",
 "libc",
 "libloading",
]

[[package]]
name = "clap"
version = "4.5.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed93b9805f8ba930df42c2590f05453d5ec36cbb85d018868a5b24d31f6ac000"
dependencies = [
 "clap_builder",
 "clap_derive",
]

[[package]]
name = "clap_builder"
version = "4.5.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "379026ff283facf611b0ea629334361c4211d1b12ee01024eec1591133b04120"
dependencies = [
 "anstream",
 "anstyle",
 "clap_lex",
 "strsim 0.11.1",
 "terminal_size",
]

[[package]]
name = "clap_complete"
version = "4.5.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c91d3baa3bcd889d60e6ef28874126a0b384fd225ab83aa6d8a801c519194ce1"
dependencies = [
 "clap",
]

[[package]]
name = "clap_complete_command"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "183495371ea78d4c9ff638bfc6497d46fed2396e4f9c50aebc1278a4a9919a3d"
dependencies = [
 "clap",
 "clap_complete",
 "clap_complete_fig",
 "clap_complete_nushell",
]

[[package]]
name = "clap_complete_fig"
version = "4.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d494102c8ff3951810c72baf96910b980fb065ca5d3101243e6a8dc19747c86b"
dependencies = [
 "clap",
 "clap_complete",
]

[[package]]
name = "clap_complete_nushell"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d02bc8b1a18ee47c4d2eec3fb5ac034dc68ebea6125b1509e9ccdffcddce66e"
dependencies = [
 "clap",
 "clap_complete",
]

[[package]]
name = "clap_derive"
version = "4.5.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09176aae279615badda0765c0c0b3f6ed53f4709118af73cf4655d85d1530cd7"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "clap_lex"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f46ad14479a25103f283c0f10005961cf086d8dc42205bb44c46ac563475dca6"

[[package]]
name = "clio"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b7fc6734af48458f72f5a3fa7b840903606427d98a710256e808f76a965047d9"
dependencies = [
 "cfg-if",
 "clap",
 "is-terminal",
 "libc",
 "tempfile",
 "walkdir",
 "windows-sys 0.42.0",
]

[[package]]
name = "clipboard-win"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15efe7a882b08f34e38556b14f2fb3daa98769d06c7f0c1b076dfd0d983bc892"
dependencies = [
 "error-code",
]

[[package]]
name = "clru"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbd0f76e066e64fdc5631e3bb46381254deab9ef1158292f27c8c57e3bf3fe59"

[[package]]
name = "cmake"
version = "0.1.54"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7caa3f9de89ddbe2c607f4101924c5abec803763ae9534e4f4d7d8f84aa81f0"
dependencies = [
 "cc",
]

[[package]]
name = "coarsetime"
version = "0.1.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91849686042de1b41cd81490edc83afbcb0abe5a9b6f2c4114f23ce8cca1bcf4"
dependencies = [
 "libc",
 "wasix",
 "wasm-bindgen",
]

[[package]]
name = "cobs"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67ba02a97a2bd10f4b59b25c7973101c79642302776489e030cd13cdab09ed15"

[[package]]
name = "codeq"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a72a59f33777d39160f5b4a8a258732cb6b538607c28b65eaa3b96f7cd35b994"
dependencies = [
 "anyhow",
 "byteorder",
 "crc32fast",
 "derive_more",
 "serde",
]

[[package]]
name = "color-eyre"
version = "0.6.2"
source = "git+https://github.com/eyre-rs/eyre.git?rev=e5d92c3#e5d92c3ad2ff0bd4c3071528441c611f201e0d8e"
dependencies = [
 "backtrace",
 "color-spantrace",
 "eyre",
 "indenter",
 "once_cell",
 "owo-colors",
 "tracing-error",
]

[[package]]
name = "color-spantrace"
version = "0.2.1"
source = "git+https://github.com/eyre-rs/eyre.git?rev=e5d92c3#e5d92c3ad2ff0bd4c3071528441c611f201e0d8e"
dependencies = [
 "once_cell",
 "owo-colors",
 "tracing-core",
 "tracing-error",
]

[[package]]
name = "colorchoice"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b63caa9aa9397e2d9480a9b13673856c78d8ac123288526c37d7839f2a86990"

[[package]]
name = "colorchoice-clap"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96a804da36d925510ac4d61a0ee8edfdba6ae00c7d5c93c8bf58f25915966956"
dependencies = [
 "clap",
 "colorchoice",
]

[[package]]
name = "colored"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "117725a109d387c937a1533ce01b450cbde6b88abceea8473c4d7a85853cda3c"
dependencies = [
 "lazy_static",
 "windows-sys 0.59.0",
]

[[package]]
name = "combine"
version = "4.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba5a308b75df32fe02788e748662718f03fde005016435c444eea572398219fd"
dependencies = [
 "bytes",
 "futures-core",
 "memchr",
 "pin-project-lite",
 "tokio",
 "tokio-util",
]

[[package]]
name = "comfy-table"
version = "7.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a65ebfec4fb190b6f90e944a817d60499ee0744e582530e2c9900a22e591d9a"
dependencies = [
 "crossterm",
 "unicode-segmentation",
 "unicode-width 0.2.0",
]

[[package]]
name = "compare"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "120133d4db2ec47efe2e26502ee984747630c67f51974fca0b6c1340cf2368d3"

[[package]]
name = "concurrent-queue"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ca0197aee26d1ae37445ee532fefce43251d24cc7c166799f4d46817f1d3973"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "condtype"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf0a07a401f374238ab8e2f11a104d2851bf9ce711ec69804834de8af45c7af"

[[package]]
name = "console"
version = "0.15.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "054ccb5b10f9f2cbf51eb355ca1d05c2d279ce1804688d0db74b4733a5aeafd8"
dependencies = [
 "encode_unicode",
 "libc",
 "once_cell",
 "unicode-width 0.2.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "console_log"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be8aed40e4edbf4d3b4431ab260b63fdc40f5780a4766824329ea0f1eefe3c0f"
dependencies = [
 "log",
 "web-sys",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-random"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87e00182fe74b066627d63b85fd550ac2998d4b0bd86bfed477a0ae4c7c71359"
dependencies = [
 "const-random-macro",
]

[[package]]
name = "const-random-macro"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d839f2a20b0aee515dc581a6172f2321f96cab76c1a38a4c584a194955390e"
dependencies = [
 "getrandom 0.2.16",
 "once_cell",
 "tiny-keccak",
]

[[package]]
name = "const_panic"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2459fc9262a1aa204eb4b5764ad4f189caec88aea9634389c0a25f8be7f6265e"

[[package]]
name = "constant_time_eq"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c74b8349d32d297c9134b8c88677813a227df8f779daa29bfc29c183fe3dca6"

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "convert_case"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec182b0ca2f35d8fc196cf3404988fd8b8c739a4d270ff118a398feb0cbec1ca"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "convert_case"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baaaa0ecca5b51987b9423ccdc971514dd8b0bb7b4060b983d3664dad3f1f89f"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "cookie"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ddef33a339a91ea89fb53151bd0a4689cfce27055c291dfa69945475d22c747"
dependencies = [
 "aes-gcm",
 "base64 0.22.1",
 "hkdf",
 "hmac",
 "percent-encoding",
 "rand 0.8.5",
 "sha2",
 "subtle",
 "time",
 "version_check",
]

[[package]]
name = "cookie_store"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2eac901828f88a5241ee0600950ab981148a18f2f756900ffba1b125ca6a3ef9"
dependencies = [
 "cookie",
 "document-features",
 "idna",
 "log",
 "publicsuffix",
 "serde",
 "serde_derive",
 "serde_json",
 "time",
 "url",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b55271e5c8c478ad3f38ad24ef34923091e0548492a266d19b3c0b4d82574c63"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773648b94d0e5d620f64f280777445740e61fe701025087ec8b57f45c791888b"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpp_demangle"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96e58d342ad113c2b878f16d5d034c03be492ae460cdbc02b7f0f2284d310c7d"
dependencies = [
 "cfg-if",
]

[[package]]
name = "cpufeatures"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59ed5838eebb26a2bb2e58f6d5b5316989ae9d08bab10e0e6d103e656d1b0280"
dependencies = [
 "libc",
]

[[package]]
name = "cranelift-bforest"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ba4f80548f22dc9c43911907b5e322c5555544ee85f785115701e6a28c9abe1"
dependencies = [
 "cranelift-entity",
]

[[package]]
name = "cranelift-bitset"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "005884e3649c3e5ff2dc79e8a94b138f11569cc08a91244a292714d2a86e9156"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "cranelift-codegen"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe4036255ec33ce9a37495dfbcfc4e1118fd34e693eff9a1e106336b7cd16a9b"
dependencies = [
 "bumpalo",
 "cranelift-bforest",
 "cranelift-bitset",
 "cranelift-codegen-meta",
 "cranelift-codegen-shared",
 "cranelift-control",
 "cranelift-entity",
 "cranelift-isle",
 "gimli 0.31.1",
 "hashbrown 0.14.5",
 "log",
 "regalloc2",
 "rustc-hash 2.1.1",
 "serde",
 "smallvec",
 "target-lexicon 0.12.16",
]

[[package]]
name = "cranelift-codegen-meta"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7ca74f4b68319da11d39e894437cb6e20ec7c2e11fbbda823c3bf207beedff7"
dependencies = [
 "cranelift-codegen-shared",
]

[[package]]
name = "cranelift-codegen-shared"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "897e54f433a0269c4187871aa06d452214d5515d228d5bdc22219585e9eef895"

[[package]]
name = "cranelift-control"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29cb4018f5bf59fb53f515fa9d80e6f8c5ce19f198dc538984ebd23ecf8965ec"
dependencies = [
 "arbitrary",
]

[[package]]
name = "cranelift-entity"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "305399fd781a2953ac78c1396f02ff53144f39c33eb7fc7789cf4e8936d13a96"
dependencies = [
 "cranelift-bitset",
 "serde",
 "serde_derive",
]

[[package]]
name = "cranelift-frontend"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9230b460a128d53653456137751d27baf567947a3ab8c0c4d6e31fd08036d81e"
dependencies = [
 "cranelift-codegen",
 "log",
 "smallvec",
 "target-lexicon 0.12.16",
]

[[package]]
name = "cranelift-isle"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b961e24ae3ec9813a24a15ae64bbd2a42e4de4d79a7f3225a412e3b94e78d1c8"

[[package]]
name = "cranelift-native"
version = "0.114.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d5bd76df6c9151188dfa428c863b33da5b34561b67f43c0cf3f24a794f9fa1f"
dependencies = [
 "cranelift-codegen",
 "libc",
 "target-lexicon 0.12.16",
]

[[package]]
name = "crc"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9710d3b3739c2e349eb44fe848ad0b7c8cb1e42bd87ee49371df2f7acaf3e675"
dependencies = [
 "crc-catalog",
]

[[package]]
name = "crc-catalog"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19d374276b40fb8bbdee95aef7c7fa6b5316ec764510eb64b8dd0e2ed0d7e7f5"

[[package]]
name = "crc32c"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a47af21622d091a8f0fb295b88bc886ac74efcc613efc19f5d0b21de5c89e47"
dependencies = [
 "rustc_version",
]

[[package]]
name = "crc32fast"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a97769d94ddab943e4510d138150169a2758b5ef3eb191a9ee688de3e23ef7b3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "critical-section"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "790eea4361631c5e7d22598ecd5723ff611904e3344ce8720784c93e3d83d40b"

[[package]]
name = "cron"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f8c3e73077b4b4a6ab1ea5047c37c57aee77657bc8ecd6f29b0af082d0b0c07"
dependencies = [
 "chrono",
 "nom 7.1.3",
 "once_cell",
]

[[package]]
name = "crossbeam"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1137cd7e7fc0fb5d3c5a8678be38ec56e819125d8d7907411fe24ccb943faca8"
dependencies = [
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-epoch",
 "crossbeam-queue",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82b8f8f868b36967f9606790d1903570de9ceaf870a7bf9fbbd3016d636a2cb2"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9dd111b7b7f7d55b72c0a6ae361660ee5853c9af73f70c3c2ef6858b950e2e51"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-queue"
version = "0.3.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f58bbc28f91df819d0aa2a2c00cd19754769c2fad90579b3592b1c9ba7a3115"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0a5c400df2834b80a4c3327b3aad3a4c4cd4de0629063962b03235697506a28"

[[package]]
name = "crossterm"
version = "0.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "829d955a0bb380ef178a640b91779e3987da38c9aea133b20614cfed8cdea9c6"
dependencies = [
 "bitflags 2.9.0",
 "crossterm_winapi",
 "parking_lot 0.12.3",
 "rustix 0.38.44",
 "winapi",
]

[[package]]
name = "crossterm_winapi"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acdd7c62a3665c7f6830a51635d9ac9b23ed385797f70a83bb8bafe9c572ab2b"
dependencies = [
 "winapi",
]

[[package]]
name = "crs-definitions"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52a5c99491ee16d7f1549494bcab90bdfb2f283ef48a25fc6c870e7e7c9f12bb"

[[package]]
name = "crunchy"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43da5946c66ffcc7745f48db692ffbb10a83bfe0afd96235c5c2a4fb23994929"

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "csv"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acdc4883a9c96732e4733212c01447ebd805833b7275a73ca3ee080fd77afdaf"
dependencies = [
 "csv-core",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "csv-core"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d02f3b0da4c6504f86e9cd789d8dbafab48c2321be74e9987593de5a894d93d"
dependencies = [
 "memchr",
]

[[package]]
name = "ct-codecs"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd0d274c65cbc1c34703d2fc2ce0fb892ff68f4516b677671a2f238a30b9b2b2"

[[package]]
name = "ctor"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a2785755761f3ddc1492979ce1e48d2c00d09311c39e4466429188f3dd6501"
dependencies = [
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "ctrlc"
version = "3.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46f93780a459b7d656ef7f071fe699c4d3d2cb201c4b24d085b6ddc505276e73"
dependencies = [
 "nix 0.30.1",
 "windows-sys 0.59.0",
]

[[package]]
name = "darling"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc7f46116c46ff9ab3eb1597a45688b6715c6e628b5c133e288e709a29bcb4ee"
dependencies = [
 "darling_core",
 "darling_macro",
]

[[package]]
name = "darling_core"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d00b9596d185e565c2207a0b01f8bd1a135483d02d9b7b0a54b11da8d53412e"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2",
 "quote",
 "strsim 0.11.1",
 "syn 2.0.101",
]

[[package]]
name = "darling_macro"
version = "0.20.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc34b93ccb385b40dc71c6fceac4b2ad23662c7eeb248cf10d529b7e055b6ead"
dependencies = [
 "darling_core",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dary_heap"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04d2cd9c18b9f454ed67da600630b021a8a80bf33f8c95896ab33aaf1c26b728"

[[package]]
name = "dashmap"
version = "5.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "978747c1d849a7d2ee5e8adc0159961c48fb7e5db2f06af6723b80123bb53856"
dependencies = [
 "cfg-if",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "dashmap"
version = "6.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5041cc499144891f3790297212f32a74fb938e5136a14943f338ef9e0ae276cf"
dependencies = [
 "cfg-if",
 "crossbeam-utils",
 "hashbrown 0.14.5",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.10",
 "serde",
]

[[package]]
name = "data-encoding"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2330da5de22e8a3cb63252ce2abb30116bf5265e89c0e01bc17015ce30a476"

[[package]]
name = "databend-bendsave"
version = "0.1.0"
dependencies = [
 "anyhow",
 "bytes",
 "clap",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-config",
 "databend-common-license",
 "databend-common-meta-client",
 "databend-common-meta-control",
 "databend-common-meta-types",
 "databend-common-storage",
 "databend-common-users",
 "databend-common-version",
 "databend-enterprise-query",
 "databend-meta",
 "databend-query",
 "form_urlencoded",
 "futures",
 "http 1.3.1",
 "log",
 "logforth",
 "opendal",
 "tokio",
 "toml 0.8.22",
]

[[package]]
name = "databend-binaries"
version = "0.1.0"
dependencies = [
 "clap",
 "databend-common-base",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-license",
 "databend-common-meta-client",
 "databend-common-metrics",
 "databend-common-storage",
 "databend-common-tracing",
 "databend-common-version",
 "databend-enterprise-query",
 "databend-query",
 "databend-storages-common-table-meta",
 "limits-rs",
 "log",
 "opendal",
 "serde",
 "serde_json",
 "serfig",
 "tokio",
]

[[package]]
name = "databend-codegen"
version = "0.1.0"
dependencies = [
 "databend-common-expression",
 "itertools 0.13.0",
]

[[package]]
name = "databend-common-ast"
version = "0.2.1"
dependencies = [
 "databend_educe",
 "derive-visitor",
 "divan",
 "enum-as-inner",
 "ethnum",
 "fast-float2",
 "fastrace",
 "goldenfile",
 "indent",
 "itertools 0.13.0",
 "logos",
 "nom 7.1.3",
 "nom-rule",
 "ordered-float 5.0.0",
 "percent-encoding",
 "pratt",
 "pretty_assertions",
 "recursive",
 "rspack-codespan-reporting",
 "serde",
 "serde_json",
 "strsim 0.10.0",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "unindent",
 "url",
]

[[package]]
name = "databend-common-auth"
version = "0.1.0"
dependencies = [
 "base64 0.22.1",
 "chrono",
 "databend-common-base",
 "http 1.3.1",
 "tempfile",
]

[[package]]
name = "databend-common-base"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "async-backtrace",
 "async-trait",
 "borsh",
 "bytemuck",
 "bytes",
 "bytesize",
 "chrono",
 "concurrent-queue",
 "crc32fast",
 "ctrlc",
 "databend-common-exception",
 "enquote",
 "fastrace",
 "futures",
 "hickory-resolver",
 "libc",
 "log",
 "logcall",
 "micromarshal",
 "num-traits",
 "num_cpus",
 "once_cell",
 "parking_lot 0.12.3",
 "pin-project-lite",
 "pprof",
 "procfs",
 "prometheus-client 0.22.3",
 "prometheus-parse",
 "quickcheck",
 "rand 0.8.5",
 "regex",
 "replace_with",
 "reqwest",
 "reqwest-hickory-resolver",
 "rustix 0.38.44",
 "serde",
 "serde_json",
 "serde_test",
 "state",
 "tikv-jemalloc-ctl",
 "tikv-jemalloc-sys",
 "tokio",
 "unicode-segmentation",
 "uuid",
]

[[package]]
name = "databend-common-building"
version = "0.1.0"
dependencies = [
 "anyhow",
 "base64 0.22.1",
 "cargo-license",
 "cargo_metadata",
 "gix",
 "log",
 "vergen",
]

[[package]]
name = "databend-common-cache"
version = "0.1.0"
dependencies = [
 "hashbrown 0.15.3",
 "hashlink 0.8.4",
]

[[package]]
name = "databend-common-catalog"
version = "0.1.0"
dependencies = [
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "chrono",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-io",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-pipeline-core",
 "databend-common-settings",
 "databend-common-storage",
 "databend-common-users",
 "databend-storages-common-session",
 "databend-storages-common-table-meta",
 "dyn-clone",
 "goldenfile",
 "log",
 "maplit",
 "parking_lot 0.12.3",
 "parquet",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "sha2",
 "thrift",
 "typetag",
 "xorf",
]

[[package]]
name = "databend-common-cloud-control"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "chrono",
 "databend-common-base",
 "databend-common-exception",
 "hyper-util",
 "lenient_semver",
 "prost",
 "prost-build",
 "semver",
 "serde",
 "tonic",
 "tonic-build",
 "tower 0.5.2",
]

[[package]]
name = "databend-common-column"
version = "0.1.0"
dependencies = [
 "arrow-buffer",
 "arrow-data",
 "arrow-schema",
 "borsh",
 "bytemuck",
 "databend-common-base",
 "databend-common-exception",
 "either",
 "ethnum",
 "foreign_vec",
 "hex",
 "log",
 "match-template",
 "num-traits",
 "proptest",
 "serde",
 "serde_derive",
 "serde_json",
 "simdutf8",
]

[[package]]
name = "databend-common-compress"
version = "0.1.0"
dependencies = [
 "async-compression 0.4.3",
 "brotli 3.5.0",
 "bytes",
 "databend-common-exception",
 "env_logger 0.11.8",
 "futures",
 "log",
 "pin-project",
 "rand 0.8.5",
 "serde",
 "tokio",
 "zip",
]

[[package]]
name = "databend-common-config"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-grpc",
 "databend-common-meta-app",
 "databend-common-storage",
 "databend-common-tracing",
 "databend-common-version",
 "log",
 "pretty_assertions",
 "serde",
 "serde_ignored",
 "serde_with",
 "serfig",
 "tempfile",
 "toml 0.8.22",
]

[[package]]
name = "databend-common-datavalues"
version = "0.1.0"
dependencies = [
 "enum-as-inner",
 "enum_dispatch",
 "serde",
 "serde_json",
]

[[package]]
name = "databend-common-exception"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrow-flight",
 "arrow-schema",
 "backtrace",
 "bincode 2.0.1",
 "cidr",
 "databend-common-ast",
 "geozero",
 "gimli 0.31.1",
 "http 1.3.1",
 "hyper 1.6.0",
 "libc",
 "object",
 "once_cell",
 "opendal",
 "parquet",
 "paste",
 "prost",
 "redis",
 "reqwest",
 "rustc-demangle",
 "serde",
 "serde_json",
 "sqlx",
 "tantivy",
 "thiserror 1.0.69",
 "tonic",
]

[[package]]
name = "databend-common-expression"
version = "0.1.0"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-data",
 "arrow-flight",
 "arrow-ipc",
 "arrow-ord",
 "arrow-schema",
 "arrow-select",
 "async-backtrace",
 "base64 0.22.1",
 "borsh",
 "bumpalo",
 "comfy-table",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-column",
 "databend-common-datavalues",
 "databend-common-exception",
 "databend-common-grpc",
 "databend-common-hashtable",
 "databend-common-io",
 "databend_educe",
 "divan",
 "either",
 "enum-as-inner",
 "ethnum",
 "futures",
 "geo",
 "geozero",
 "goldenfile",
 "hex",
 "hyper-util",
 "itertools 0.13.0",
 "jiff 0.2.13",
 "jsonb",
 "lexical-core",
 "log",
 "match-template",
 "memchr",
 "micromarshal",
 "num-bigint",
 "num-traits",
 "pretty_assertions",
 "proptest",
 "rand 0.8.5",
 "rand_distr",
 "recursive",
 "roaring",
 "rust_decimal",
 "rustls 0.23.27",
 "serde",
 "serde_json",
 "strength_reduce",
 "terminal_size",
 "tonic",
 "typetag",
 "unicode-segmentation",
]

[[package]]
name = "databend-common-formats"
version = "0.1.0"
dependencies = [
 "aho-corasick",
 "async-trait",
 "base64 0.22.1",
 "bstr",
 "chrono-tz 0.8.6",
 "databend-common-base",
 "databend-common-column",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-io",
 "databend-common-meta-app",
 "databend-common-settings",
 "databend-functions-scalar-datetime",
 "databend-storages-common-blocks",
 "databend-storages-common-table-meta",
 "geozero",
 "goldenfile",
 "hex",
 "jiff 0.2.13",
 "jsonb",
 "lexical-core",
 "match-template",
 "micromarshal",
 "num",
 "num-traits",
 "pretty_assertions",
 "roaring",
 "serde_json",
 "tokio",
]

[[package]]
name = "databend-common-functions"
version = "0.1.0"
dependencies = [
 "base64 0.22.1",
 "blake3",
 "borsh",
 "bstr",
 "bumpalo",
 "comfy-table",
 "crc32fast",
 "ctor",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-column",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-hashtable",
 "databend-common-io",
 "databend-common-openai",
 "databend-common-vector",
 "databend-functions-scalar-arithmetic",
 "databend-functions-scalar-datetime",
 "databend-functions-scalar-decimal",
 "databend-functions-scalar-geo",
 "databend-functions-scalar-integer-basic-arithmetic",
 "databend-functions-scalar-math",
 "databend-functions-scalar-numeric-basic-arithmetic",
 "divan",
 "geo",
 "geohash",
 "geozero",
 "glob",
 "goldenfile",
 "h3o",
 "hex",
 "itertools 0.13.0",
 "jaq-core",
 "jaq-interpret",
 "jaq-parse",
 "jaq-std",
 "jiff 0.2.13",
 "jsonb",
 "lexical-core",
 "libm",
 "match-template",
 "md-5",
 "naive-cityhash",
 "num-traits",
 "once_cell",
 "proj4rs",
 "proptest",
 "rand 0.8.5",
 "regex",
 "roaring",
 "serde",
 "serde_json",
 "sha1",
 "sha2",
 "simdutf8",
 "simple_hll",
 "siphasher 0.3.11",
 "strength_reduce",
 "stringslice",
 "twox-hash 1.6.3",
 "unicase",
]

[[package]]
name = "databend-common-grpc"
version = "0.1.0"
dependencies = [
 "anyerror",
 "databend-common-base",
 "databend-common-exception",
 "hickory-resolver",
 "hyper 1.6.0",
 "hyper-util",
 "jwt-simple",
 "log",
 "serde",
 "thiserror 1.0.69",
 "tonic",
 "tower-service",
]

[[package]]
name = "databend-common-hashtable"
version = "0.1.0"
dependencies = [
 "ahash 0.8.12",
 "bumpalo",
 "cfg-if",
 "databend-common-base",
 "databend-common-column",
 "ethnum",
 "rand 0.8.5",
]

[[package]]
name = "databend-common-http"
version = "0.1.0"
dependencies = [
 "anyerror",
 "databend-common-base",
 "databend-common-exception",
 "futures",
 "http 1.3.1",
 "log",
 "poem",
 "pretty_assertions",
 "serde",
 "tempfile",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-io"
version = "0.1.0"
dependencies = [
 "aho-corasick",
 "bincode 2.0.1",
 "borsh",
 "bytes",
 "chrono",
 "chrono-tz 0.8.6",
 "databend-common-base",
 "databend-common-exception",
 "enquote",
 "enumflags2",
 "ethnum",
 "geo",
 "geozero",
 "hex",
 "jiff 0.2.13",
 "lexical-core",
 "micromarshal",
 "rmp-serde",
 "roaring",
 "scroll 0.12.0",
 "serde",
 "wkt",
]

[[package]]
name = "databend-common-license"
version = "0.1.0"
dependencies = [
 "databend-common-base",
 "databend-common-exception",
 "display-more",
 "jwt-simple",
 "serde",
 "serde_json",
]

[[package]]
name = "databend-common-management"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-cache",
 "databend-common-meta-client",
 "databend-common-meta-kvapi",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-proto-conv",
 "databend-common-version",
 "enumflags2",
 "fastrace",
 "futures",
 "log",
 "prost",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-meta-api"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-backtrace",
 "async-trait",
 "chrono",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-meta-kvapi",
 "databend-common-meta-stoerr",
 "databend-common-meta-types",
 "databend-common-proto-conv",
 "display-more",
 "fastrace",
 "futures",
 "itertools 0.13.0",
 "log",
 "logcall",
 "maplit",
 "prost",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tonic",
]

[[package]]
name = "databend-common-meta-app"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "chrono",
 "databend-common-ast",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-io",
 "databend-common-meta-app-storage",
 "databend-common-meta-app-types",
 "databend-common-meta-kvapi",
 "databend-common-meta-types",
 "derive_more",
 "display-more",
 "enumflags2",
 "hex",
 "itertools 0.13.0",
 "maplit",
 "num-derive",
 "num-traits",
 "paste",
 "prost",
 "serde",
 "serde_json",
 "sha1",
 "sha2",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-meta-app-storage"
version = "0.1.0"
dependencies = [
 "databend-common-base",
 "databend-common-exception",
 "opendal",
 "serde",
]

[[package]]
name = "databend-common-meta-app-types"
version = "0.1.0"
dependencies = [
 "anyhow",
 "num-derive",
 "prost",
 "serde",
]

[[package]]
name = "databend-common-meta-cache"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "databend-common-base",
 "databend-common-meta-client",
 "databend-common-meta-types",
 "futures",
 "log",
 "pretty_assertions",
 "sub-cache",
 "tonic",
]

[[package]]
name = "databend-common-meta-client"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "arrow-flight",
 "async-backtrace",
 "chrono",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-grpc",
 "databend-common-meta-api",
 "databend-common-meta-kvapi",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-tracing",
 "databend-common-version",
 "derive_more",
 "display-more",
 "fastrace",
 "futures",
 "itertools 0.13.0",
 "log",
 "logcall",
 "once_cell",
 "parking_lot 0.12.3",
 "pretty_assertions",
 "prost",
 "rand 0.8.5",
 "semver",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tonic",
]

[[package]]
name = "databend-common-meta-control"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "databend-common-building",
 "databend-common-meta-client",
 "databend-common-meta-raft-store",
 "databend-common-meta-sled-store",
 "databend-common-meta-types",
 "databend-common-tracing",
 "databend-meta",
 "futures",
 "raft-log",
 "reqwest",
 "serde",
 "serde_json",
 "tokio",
 "tokio-stream",
 "url",
]

[[package]]
name = "databend-common-meta-kvapi"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "databend-common-meta-app-types",
 "databend-common-meta-types",
 "display-more",
 "futures-util",
 "log",
 "serde",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-meta-kvapi-test-suite"
version = "0.1.0"
dependencies = [
 "anyhow",
 "databend-common-meta-kvapi",
 "databend-common-meta-types",
 "display-more",
 "fastrace",
 "log",
 "tokio",
]

[[package]]
name = "databend-common-meta-process"
version = "0.1.0"
dependencies = [
 "anyhow",
 "clap",
 "databend-common-meta-app",
 "databend-common-meta-kvapi",
 "databend-common-meta-raft-store",
 "databend-common-meta-sled-store",
 "databend-common-meta-types",
 "databend-common-proto-conv",
 "databend-common-protos",
 "databend-common-tracing",
 "openraft",
 "prost",
 "serde",
 "serde_json",
]

[[package]]
name = "databend-common-meta-raft-store"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "bincode 2.0.1",
 "byteorder",
 "chrono",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-grpc",
 "databend-common-meta-kvapi",
 "databend-common-meta-sled-store",
 "databend-common-meta-stoerr",
 "databend-common-meta-types",
 "databend-common-version",
 "deepsize",
 "derive_more",
 "display-more",
 "fastrace",
 "fs_extra",
 "futures",
 "futures-async-stream",
 "futures-util",
 "hostname",
 "log",
 "map-api",
 "maplit",
 "num",
 "openraft",
 "pretty_assertions",
 "raft-log",
 "rmp-serde",
 "rotbl",
 "semver",
 "serde",
 "serde_json",
 "stream-more",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "tempfile",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "databend-common-meta-semaphore"
version = "0.1.0"
dependencies = [
 "anyhow",
 "codeq",
 "databend-common-base",
 "databend-common-meta-client",
 "databend-common-meta-kvapi",
 "databend-common-meta-types",
 "display-more",
 "futures",
 "itertools 0.13.0",
 "log",
 "pretty_assertions",
 "thiserror 1.0.69",
 "tokio",
 "tonic",
]

[[package]]
name = "databend-common-meta-sled-store"
version = "0.1.0"
dependencies = [
 "anyerror",
 "byteorder",
 "databend-common-meta-stoerr",
 "databend-common-meta-types",
 "fastrace",
 "log",
 "openraft",
 "serde",
 "serde_json",
 "sled",
 "tempfile",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-meta-stoerr"
version = "0.1.0"
dependencies = [
 "anyerror",
 "databend-common-exception",
 "serde_json",
 "sled",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-meta-store"
version = "0.1.0"
dependencies = [
 "anyhow",
 "async-trait",
 "databend-common-base",
 "databend-common-grpc",
 "databend-common-meta-client",
 "databend-common-meta-kvapi",
 "databend-common-meta-kvapi-test-suite",
 "databend-common-meta-semaphore",
 "databend-common-meta-types",
 "databend-meta",
 "log",
 "tempfile",
 "tokio",
 "tokio-stream",
 "tonic",
]

[[package]]
name = "databend-common-meta-types"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "databend-common-exception",
 "databend-common-meta-stoerr",
 "databend-common-tracing",
 "deepsize",
 "derive_more",
 "display-more",
 "futures-util",
 "log",
 "map-api",
 "num-derive",
 "num-traits",
 "openraft",
 "pretty_assertions",
 "prost",
 "prost-build",
 "rotbl",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
 "tonic",
 "tonic-build",
]

[[package]]
name = "databend-common-metrics"
version = "0.1.0"
dependencies = [
 "anyhow",
 "databend-common-base",
 "procfs",
 "prometheus-client 0.22.3",
]

[[package]]
name = "databend-common-native"
version = "0.1.0"
dependencies = [
 "ahash 0.8.12",
 "bitpacking 0.8.4",
 "bytemuck",
 "byteorder",
 "bytes",
 "databend-common-column",
 "databend-common-expression",
 "env_logger 0.11.8",
 "hashbrown 0.14.5",
 "log",
 "lz4",
 "match-template",
 "num",
 "opendal",
 "rand 0.8.5",
 "ringbuffer",
 "roaring",
 "serde",
 "serde_json",
 "snap",
 "zstd 0.12.4",
]

[[package]]
name = "databend-common-openai"
version = "0.1.0"
dependencies = [
 "databend-common-exception",
 "databend-common-metrics",
 "log",
 "openai_api_rust",
]

[[package]]
name = "databend-common-pipeline-core"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "fastrace",
 "futures",
 "log",
 "petgraph 0.6.5",
 "serde",
 "tokio",
 "typetag",
]

[[package]]
name = "databend-common-pipeline-sinks"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-pipeline-core",
 "tokio",
]

[[package]]
name = "databend-common-pipeline-sources"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-pipeline-core",
 "futures",
 "parking_lot 0.12.3",
]

[[package]]
name = "databend-common-pipeline-transforms"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-pipeline-core",
 "itertools 0.13.0",
 "jsonb",
 "log",
 "match-template",
 "rand 0.8.5",
 "serde",
 "tokio",
 "typetag",
]

[[package]]
name = "databend-common-proto-conv"
version = "0.1.0"
dependencies = [
 "anyhow",
 "chrono",
 "convert_case 0.6.0",
 "databend-common-expression",
 "databend-common-io",
 "databend-common-meta-app",
 "databend-common-meta-app-types",
 "databend-common-meta-types",
 "databend-common-protos",
 "enumflags2",
 "fastrace",
 "maplit",
 "num",
 "pretty_assertions",
 "prost",
 "thiserror 1.0.69",
]

[[package]]
name = "databend-common-protos"
version = "0.1.0"
dependencies = [
 "lenient_semver",
 "num-derive",
 "num-traits",
 "prost",
 "prost-build",
 "semver",
 "tonic",
 "tonic-build",
]

[[package]]
name = "databend-common-script"
version = "0.1.0"
dependencies = [
 "databend-common-ast",
 "databend-common-exception",
 "derive-visitor",
 "fastrace",
 "goldenfile",
 "tokio",
 "unindent",
]

[[package]]
name = "databend-common-settings"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "chrono-tz 0.8.6",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-io",
 "databend-common-meta-app",
 "databend-common-users",
 "databend-common-version",
 "itertools 0.13.0",
 "log",
 "num_cpus",
 "once_cell",
 "serde",
 "serde_json",
 "sys-info",
 "tokio",
]

[[package]]
name = "databend-common-sql"
version = "0.1.0"
dependencies = [
 "ahash 0.8.12",
 "anyhow",
 "async-backtrace",
 "async-recursion",
 "async-trait",
 "chrono",
 "chrono-tz 0.8.6",
 "cidr",
 "cron",
 "ctor",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-compress",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-license",
 "databend-common-management",
 "databend-common-meta-app",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-pipeline-core",
 "databend-common-pipeline-transforms",
 "databend-common-settings",
 "databend-common-storage",
 "databend-common-storages-result-cache",
 "databend-common-storages-view",
 "databend-common-users",
 "databend-common-version",
 "databend-enterprise-data-mask-feature",
 "databend-storages-common-cache",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "databend_educe",
 "derive-visitor",
 "enum-as-inner",
 "fastrace",
 "globiter",
 "indexmap 2.9.0",
 "itertools 0.13.0",
 "jsonb",
 "log",
 "num-derive",
 "num-traits",
 "opendal",
 "parking_lot 0.12.3",
 "pretty_assertions",
 "prqlc",
 "rand 0.8.5",
 "recursive",
 "regex",
 "roaring",
 "serde",
 "serde_json",
 "sha2",
 "similar",
 "simsearch",
 "tokio",
 "unicase",
 "url",
]

[[package]]
name = "databend-common-storage"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrow-schema",
 "async-backtrace",
 "chrono",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-auth",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-metrics",
 "databend-common-native",
 "databend-enterprise-storage-encryption",
 "futures",
 "http 1.3.1",
 "iceberg",
 "log",
 "lru",
 "opendal",
 "parquet",
 "prometheus-client 0.22.3",
 "regex",
 "reqwest",
 "serde",
 "thiserror 1.0.69",
 "url",
]

[[package]]
name = "databend-common-storages-delta"
version = "0.1.0"
dependencies = [
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-storage",
 "databend-common-storages-parquet",
 "databend-storages-common-pruner",
 "databend-storages-common-table-meta",
 "deltalake",
 "fastrace",
 "object_store_opendal",
 "parquet",
 "serde",
 "serde_json",
 "tokio",
 "typetag",
 "url",
]

[[package]]
name = "databend-common-storages-factory"
version = "0.1.0"
dependencies = [
 "dashmap 6.1.0",
 "databend-common-catalog",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-meta-app",
 "databend-common-storages-delta",
 "databend-common-storages-fuse",
 "databend-common-storages-iceberg",
 "databend-common-storages-memory",
 "databend-common-storages-null",
 "databend-common-storages-random",
 "databend-common-storages-stream",
 "databend-common-storages-view",
 "databend-storages-common-index",
]

[[package]]
name = "databend-common-storages-fuse"
version = "0.1.0"
dependencies = [
 "ahash 0.8.12",
 "arrow",
 "arrow-array",
 "arrow-ipc",
 "arrow-schema",
 "async-backtrace",
 "async-channel 1.9.0",
 "async-trait",
 "backoff",
 "bytes",
 "chrono",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-hashtable",
 "databend-common-io",
 "databend-common-license",
 "databend-common-meta-app",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-native",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sinks",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-sql",
 "databend-common-storage",
 "databend-common-users",
 "databend-enterprise-fail-safe",
 "databend-enterprise-vacuum-handler",
 "databend-storages-common-blocks",
 "databend-storages-common-cache",
 "databend-storages-common-index",
 "databend-storages-common-io",
 "databend-storages-common-pruner",
 "databend-storages-common-session",
 "databend-storages-common-table-meta",
 "divan",
 "enum-as-inner",
 "fastrace",
 "futures",
 "futures-util",
 "indexmap 2.9.0",
 "itertools 0.13.0",
 "jsonb",
 "log",
 "match-template",
 "opendal",
 "parking_lot 0.12.3",
 "parquet",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "sha2",
 "siphasher 0.3.11",
 "tantivy",
 "tantivy-common",
 "tantivy-fst",
 "tantivy-jieba",
 "tempfile",
 "thrift",
 "typetag",
 "uuid",
 "xorf",
]

[[package]]
name = "databend-common-storages-hive"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-recursion",
 "async-trait",
 "chrono",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-app",
 "databend-common-meta-types",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-sql",
 "databend-common-storage",
 "databend-common-storages-parquet",
 "databend-storages-common-pruner",
 "databend-storages-common-table-meta",
 "fastrace",
 "faststr",
 "futures",
 "hive_metastore",
 "log",
 "opendal",
 "parquet",
 "recursive",
 "serde",
 "typetag",
 "volo-thrift",
]

[[package]]
name = "databend-common-storages-iceberg"
version = "0.1.0"
dependencies = [
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "chrono",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-meta-types",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-storage",
 "databend-common-storages-orc",
 "databend-common-storages-parquet",
 "databend-storages-common-cache",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "databend_educe",
 "fastrace",
 "futures",
 "iceberg",
 "iceberg-catalog-glue",
 "iceberg-catalog-hms",
 "iceberg-catalog-rest",
 "iceberg-catalog-s3tables",
 "log",
 "serde",
 "serde_json",
 "uuid",
]

[[package]]
name = "databend-common-storages-information-schema"
version = "0.1.0"
dependencies = [
 "databend-common-ast",
 "databend-common-catalog",
 "databend-common-meta-app",
 "databend-common-storages-system",
 "databend-common-storages-view",
]

[[package]]
name = "databend-common-storages-memory"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sinks",
 "databend-common-pipeline-sources",
 "databend-common-storage",
 "databend-storages-common-blocks",
 "databend-storages-common-table-meta",
 "parking_lot 0.12.3",
 "serde",
 "typetag",
]

[[package]]
name = "databend-common-storages-null"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sinks",
 "databend-common-pipeline-sources",
 "databend-storages-common-table-meta",
]

[[package]]
name = "databend-common-storages-orc"
version = "0.1.0"
dependencies = [
 "arrow-array",
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "bytes",
 "chrono",
 "dashmap 6.1.0",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-storage",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "futures-util",
 "log",
 "opendal",
 "orc-rust",
 "serde",
 "serde_json",
 "typetag",
]

[[package]]
name = "databend-common-storages-parquet"
version = "0.1.0"
dependencies = [
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "bytes",
 "chrono",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-app",
 "databend-common-metrics",
 "databend-common-pipeline-core",
 "databend-common-settings",
 "databend-common-storage",
 "databend-storages-common-cache",
 "databend-storages-common-pruner",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "futures",
 "jiff 0.2.13",
 "log",
 "opendal",
 "parquet",
 "rand 0.8.5",
 "serde",
 "thrift",
 "typetag",
]

[[package]]
name = "databend-common-storages-random"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-storages-common-table-meta",
 "serde",
 "typetag",
]

[[package]]
name = "databend-common-storages-result-cache"
version = "0.1.0"
dependencies = [
 "arrow",
 "async-backtrace",
 "async-trait",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-meta-kvapi",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sinks",
 "databend-common-storage",
 "databend-common-storages-parquet",
 "databend-storages-common-blocks",
 "databend-storages-common-table-meta",
 "opendal",
 "parquet",
 "serde",
 "serde_json",
 "sha2",
 "tokio",
 "uuid",
]

[[package]]
name = "databend-common-storages-stage"
version = "0.1.0"
dependencies = [
 "apache-avro",
 "arrow-schema",
 "async-backtrace",
 "async-trait",
 "bstr",
 "csv-core",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-column",
 "databend-common-compress",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-formats",
 "databend-common-functions",
 "databend-common-io",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-settings",
 "databend-common-storage",
 "databend-common-storages-orc",
 "databend-common-storages-parquet",
 "databend-common-version",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "enum-as-inner",
 "futures",
 "jsonb",
 "lexical-core",
 "log",
 "match-template",
 "num-bigint",
 "num-traits",
 "opendal",
 "parking_lot 0.12.3",
 "parquet",
 "serde",
 "serde_json",
 "typetag",
]

[[package]]
name = "databend-common-storages-stream"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-sql",
 "databend-common-storages-fuse",
 "databend-storages-common-table-meta",
 "fastrace",
 "futures",
 "log",
]

[[package]]
name = "databend-common-storages-system"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "chrono",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-cloud-control",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-management",
 "databend-common-meta-app",
 "databend-common-metrics",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-sql",
 "databend-common-storage",
 "databend-common-storages-fuse",
 "databend-common-storages-null",
 "databend-common-storages-result-cache",
 "databend-common-storages-stream",
 "databend-common-storages-view",
 "databend-common-users",
 "databend-storages-common-cache",
 "databend-storages-common-table-meta",
 "futures",
 "itertools 0.13.0",
 "jiff 0.2.13",
 "jsonb",
 "log",
 "once_cell",
 "opendal",
 "parking_lot 0.12.3",
 "regex",
 "serde",
 "serde_json",
 "serde_repr",
 "snailquote",
 "tikv-jemalloc-ctl",
 "typetag",
]

[[package]]
name = "databend-common-storages-view"
version = "0.1.0"
dependencies = [
 "async-trait",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-meta-app",
]

[[package]]
name = "databend-common-tracing"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrow-array",
 "arrow-schema",
 "async-channel 1.9.0",
 "backtrace",
 "chrono",
 "concurrent-queue",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-meta-app-storage",
 "defer",
 "fastrace",
 "fastrace-opentelemetry",
 "itertools 0.13.0",
 "libc",
 "log",
 "logforth",
 "opendal",
 "opentelemetry",
 "opentelemetry-otlp",
 "opentelemetry_sdk",
 "parquet",
 "serde",
 "serde_json",
 "toml 0.8.22",
 "tonic",
]

[[package]]
name = "databend-common-users"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "base64 0.22.1",
 "chrono",
 "cidr",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-grpc",
 "databend-common-management",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-cache",
 "databend-common-meta-kvapi",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "enumflags2",
 "itertools 0.13.0",
 "jwt-simple",
 "log",
 "p256",
 "parking_lot 0.12.3",
 "passwords",
 "pretty_assertions",
 "reqwest",
 "serde",
 "serde_json",
 "wiremock",
]

[[package]]
name = "databend-common-vector"
version = "0.1.0"
dependencies = [
 "approx",
 "databend-common-exception",
 "ndarray",
]

[[package]]
name = "databend-common-version"
version = "0.1.0"
dependencies = [
 "databend-common-building",
 "semver",
]

[[package]]
name = "databend-enterprise-aggregating-index"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-meta-app",
]

[[package]]
name = "databend-enterprise-attach-table"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-meta-app",
 "databend-common-sql",
]

[[package]]
name = "databend-enterprise-data-mask-feature"
version = "0.1.0"
dependencies = [
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-meta-app",
 "databend-common-meta-store",
]

[[package]]
name = "databend-enterprise-fail-safe"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-meta-app",
]

[[package]]
name = "databend-enterprise-hilbert-clustering"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-storages-common-table-meta",
]

[[package]]
name = "databend-enterprise-meta"
version = "0.1.0"

[[package]]
name = "databend-enterprise-query"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "base64 0.22.1",
 "chrono",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-io",
 "databend-common-license",
 "databend-common-management",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-sql",
 "databend-common-storage",
 "databend-common-storages-fuse",
 "databend-common-storages-stream",
 "databend-common-tracing",
 "databend-common-version",
 "databend-enterprise-aggregating-index",
 "databend-enterprise-attach-table",
 "databend-enterprise-data-mask-feature",
 "databend-enterprise-fail-safe",
 "databend-enterprise-hilbert-clustering",
 "databend-enterprise-resources-management",
 "databend-enterprise-storage-encryption",
 "databend-enterprise-storage-quota",
 "databend-enterprise-stream-handler",
 "databend-enterprise-table-index",
 "databend-enterprise-vacuum-handler",
 "databend-enterprise-virtual-column",
 "databend-query",
 "databend-storages-common-cache",
 "databend-storages-common-io",
 "databend-storages-common-pruner",
 "databend-storages-common-table-meta",
 "derive-visitor",
 "futures",
 "futures-util",
 "jsonb",
 "jwt-simple",
 "log",
 "opendal",
 "tantivy",
 "tempfile",
 "uuid",
]

[[package]]
name = "databend-enterprise-resources-management"
version = "0.1.0"
dependencies = [
 "async-trait",
 "databend-common-base",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-management",
 "databend-common-meta-types",
]

[[package]]
name = "databend-enterprise-storage-encryption"
version = "0.1.0"
dependencies = [
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
]

[[package]]
name = "databend-enterprise-storage-quota"
version = "0.1.0"
dependencies = [
 "async-trait",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-license",
]

[[package]]
name = "databend-enterprise-stream-handler"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-meta-app",
 "databend-common-sql",
]

[[package]]
name = "databend-enterprise-table-index"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-meta-app",
 "databend-common-pipeline-core",
 "databend-common-storages-fuse",
 "databend-storages-common-table-meta",
]

[[package]]
name = "databend-enterprise-vacuum-handler"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
]

[[package]]
name = "databend-enterprise-virtual-column"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "async-trait",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-pipeline-core",
 "databend-common-storages-fuse",
]

[[package]]
name = "databend-functions-scalar-arithmetic"
version = "0.1.0"
dependencies = [
 "databend-common-expression",
 "databend-functions-scalar-decimal",
 "lexical-core",
 "match-template",
 "num-traits",
]

[[package]]
name = "databend-functions-scalar-datetime"
version = "0.1.0"
dependencies = [
 "chrono",
 "databend-common-column",
 "databend-common-exception",
 "databend-common-expression",
 "dtparse",
 "jiff 0.2.13",
 "num-traits",
]

[[package]]
name = "databend-functions-scalar-decimal"
version = "0.1.0"
dependencies = [
 "databend-common-base",
 "databend-common-expression",
 "jsonb",
 "match-template",
 "num-traits",
]

[[package]]
name = "databend-functions-scalar-geo"
version = "0.1.0"
dependencies = [
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-io",
 "geo",
 "geohash",
 "geozero",
 "h3o",
 "hex",
 "jsonb",
 "num-traits",
 "once_cell",
 "proj4rs",
]

[[package]]
name = "databend-functions-scalar-integer-basic-arithmetic"
version = "0.1.0"
dependencies = [
 "databend-common-expression",
 "databend-functions-scalar-numeric-basic-arithmetic",
 "match-template",
 "num-traits",
]

[[package]]
name = "databend-functions-scalar-math"
version = "0.1.0"
dependencies = [
 "crc32fast",
 "databend-common-base",
 "databend-common-expression",
 "databend-functions-scalar-decimal",
 "match-template",
 "num-traits",
]

[[package]]
name = "databend-functions-scalar-numeric-basic-arithmetic"
version = "0.1.0"
dependencies = [
 "databend-common-expression",
 "match-template",
 "num-traits",
 "strength_reduce",
]

[[package]]
name = "databend-meta"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "arrow-flight",
 "async-trait",
 "backon",
 "clap",
 "databend-common-base",
 "databend-common-grpc",
 "databend-common-http",
 "databend-common-meta-api",
 "databend-common-meta-cache",
 "databend-common-meta-client",
 "databend-common-meta-kvapi",
 "databend-common-meta-kvapi-test-suite",
 "databend-common-meta-raft-store",
 "databend-common-meta-semaphore",
 "databend-common-meta-sled-store",
 "databend-common-meta-stoerr",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-tracing",
 "databend-common-version",
 "deepsize",
 "derive_more",
 "display-more",
 "env_logger 0.11.8",
 "fastrace",
 "feature-set",
 "futures",
 "futures-async-stream",
 "http 1.3.1",
 "itertools 0.13.0",
 "log",
 "logcall",
 "maplit",
 "poem",
 "pretty_assertions",
 "prometheus-client 0.22.3",
 "prost",
 "raft-log",
 "regex",
 "reqwest",
 "rustls 0.23.27",
 "semver",
 "serde",
 "serde_json",
 "serfig",
 "temp-env",
 "tempfile",
 "test-harness",
 "tokio",
 "tokio-stream",
 "tonic",
 "tonic-reflection",
 "watcher",
]

[[package]]
name = "databend-meta-binaries"
version = "0.1.0"
dependencies = [
 "anyerror",
 "anyhow",
 "chrono",
 "clap",
 "databend-common-base",
 "databend-common-exception",
 "databend-common-grpc",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-client",
 "databend-common-meta-control",
 "databend-common-meta-kvapi",
 "databend-common-meta-raft-store",
 "databend-common-meta-semaphore",
 "databend-common-meta-sled-store",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-tracing",
 "databend-common-version",
 "databend-meta",
 "display-more",
 "fastrace",
 "futures",
 "log",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "tokio",
]

[[package]]
name = "databend-query"
version = "0.1.0"
dependencies = [
 "anyhow",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-flight",
 "arrow-ipc",
 "arrow-schema",
 "arrow-select",
 "arrow-udf-runtime",
 "async-backtrace",
 "async-channel 1.9.0",
 "async-compat",
 "async-recursion",
 "async-stream",
 "async-trait",
 "backoff",
 "backon",
 "base64 0.22.1",
 "buf-list",
 "bumpalo",
 "byteorder",
 "bytes",
 "chrono",
 "chrono-tz 0.8.6",
 "concurrent-queue",
 "ctor",
 "dashmap 6.1.0",
 "databend-common-ast",
 "databend-common-base",
 "databend-common-building",
 "databend-common-cache",
 "databend-common-catalog",
 "databend-common-cloud-control",
 "databend-common-column",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-formats",
 "databend-common-functions",
 "databend-common-grpc",
 "databend-common-hashtable",
 "databend-common-http",
 "databend-common-io",
 "databend-common-license",
 "databend-common-management",
 "databend-common-meta-api",
 "databend-common-meta-app",
 "databend-common-meta-app-types",
 "databend-common-meta-client",
 "databend-common-meta-kvapi",
 "databend-common-meta-semaphore",
 "databend-common-meta-store",
 "databend-common-meta-types",
 "databend-common-metrics",
 "databend-common-openai",
 "databend-common-pipeline-core",
 "databend-common-pipeline-sinks",
 "databend-common-pipeline-sources",
 "databend-common-pipeline-transforms",
 "databend-common-script",
 "databend-common-settings",
 "databend-common-sql",
 "databend-common-storage",
 "databend-common-storages-delta",
 "databend-common-storages-factory",
 "databend-common-storages-fuse",
 "databend-common-storages-hive",
 "databend-common-storages-iceberg",
 "databend-common-storages-information-schema",
 "databend-common-storages-memory",
 "databend-common-storages-null",
 "databend-common-storages-orc",
 "databend-common-storages-parquet",
 "databend-common-storages-result-cache",
 "databend-common-storages-stage",
 "databend-common-storages-stream",
 "databend-common-storages-system",
 "databend-common-storages-view",
 "databend-common-tracing",
 "databend-common-users",
 "databend-common-version",
 "databend-enterprise-aggregating-index",
 "databend-enterprise-attach-table",
 "databend-enterprise-data-mask-feature",
 "databend-enterprise-hilbert-clustering",
 "databend-enterprise-resources-management",
 "databend-enterprise-stream-handler",
 "databend-enterprise-table-index",
 "databend-enterprise-vacuum-handler",
 "databend-enterprise-virtual-column",
 "databend-storages-common-blocks",
 "databend-storages-common-cache",
 "databend-storages-common-index",
 "databend-storages-common-io",
 "databend-storages-common-session",
 "databend-storages-common-stage",
 "databend-storages-common-table-meta",
 "derive-visitor",
 "ethnum",
 "fastrace",
 "flatbuffers",
 "futures",
 "futures-util",
 "geozero",
 "goldenfile",
 "headers",
 "hex",
 "http 1.3.1",
 "humantime",
 "hyper-util",
 "indicatif",
 "itertools 0.13.0",
 "jiff 0.2.13",
 "jsonb",
 "jwt-simple",
 "log",
 "lz4",
 "maplit",
 "match-template",
 "md-5",
 "mysql_async",
 "naive-cityhash",
 "num_cpus",
 "opendal",
 "opensrv-mysql",
 "opentelemetry",
 "opentelemetry_sdk",
 "p256",
 "parking_lot 0.12.3",
 "parquet",
 "paste",
 "petgraph 0.6.5",
 "pin-project-lite",
 "poem",
 "pretty_assertions",
 "prometheus-client 0.22.3",
 "prost",
 "rand 0.8.5",
 "recursive",
 "redis",
 "regex",
 "reqwest",
 "rustls 0.23.27",
 "rustls-pemfile 2.2.0",
 "rustls-pki-types",
 "rustyline",
 "serde",
 "serde_json",
 "serde_stacker",
 "serde_urlencoded",
 "serde_yaml",
 "sha2",
 "socket2",
 "sqlx",
 "sysinfo",
 "temp-env",
 "tempfile",
 "tokio",
 "tokio-stream",
 "toml 0.8.22",
 "tonic",
 "tower 0.5.2",
 "typetag",
 "url",
 "uuid",
 "walkdir",
 "wiremock",
 "xorf",
]

[[package]]
name = "databend-sqllogictests"
version = "0.1.0"
dependencies = [
 "async-recursion",
 "async-trait",
 "bollard",
 "clap",
 "cookie",
 "databend-common-exception",
 "env_logger 0.11.8",
 "futures-util",
 "mysql_async",
 "rand 0.8.5",
 "recursive",
 "redis",
 "regex",
 "reqwest",
 "serde",
 "serde_json",
 "sqllogictest",
 "testcontainers",
 "testcontainers-modules",
 "thiserror 1.0.69",
 "tokio",
 "url",
 "walkdir",
]

[[package]]
name = "databend-sqlsmith"
version = "0.1.0"
dependencies = [
 "chrono-tz 0.8.6",
 "clap",
 "cookie",
 "databend-common-ast",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-formats",
 "databend-common-functions",
 "databend-common-io",
 "databend-common-sql",
 "derive-visitor",
 "ethnum",
 "itertools 0.13.0",
 "jiff 0.2.13",
 "jsonb",
 "rand 0.8.5",
 "reqwest",
 "serde",
 "serde_json",
 "tokio",
 "tracing",
 "tracing-subscriber",
 "url",
]

[[package]]
name = "databend-storages-common-blocks"
version = "0.1.0"
dependencies = [
 "databend-common-exception",
 "databend-common-expression",
 "databend-storages-common-table-meta",
 "parking_lot 0.12.3",
 "parquet",
]

[[package]]
name = "databend-storages-common-cache"
version = "0.1.0"
dependencies = [
 "arrow",
 "async-backtrace",
 "async-trait",
 "bytes",
 "crc32fast",
 "crossbeam-channel",
 "databend-common-base",
 "databend-common-cache",
 "databend-common-catalog",
 "databend-common-config",
 "databend-common-exception",
 "databend-common-metrics",
 "databend-storages-common-index",
 "databend-storages-common-table-meta",
 "divan",
 "hex",
 "libc",
 "log",
 "mockall",
 "parking_lot 0.12.3",
 "parquet",
 "rayon",
 "rustix 0.38.44",
 "siphasher 0.3.11",
 "tempfile",
]

[[package]]
name = "databend-storages-common-index"
version = "0.1.0"
dependencies = [
 "anyerror",
 "bincode 2.0.1",
 "bytes",
 "cbordata",
 "databend-common-ast",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-storages-common-table-meta",
 "divan",
 "fastrace",
 "goldenfile",
 "jsonb",
 "levenshtein_automata",
 "log",
 "match-template",
 "parquet",
 "rand 0.8.5",
 "roaring",
 "serde",
 "tantivy",
 "tantivy-common",
 "tantivy-fst",
 "thiserror 1.0.69",
 "xorfilter-rs",
]

[[package]]
name = "databend-storages-common-io"
version = "0.1.0"
dependencies = [
 "async-backtrace",
 "bytes",
 "databend-common-base",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-metrics",
 "fastrace",
 "futures",
 "log",
 "opendal",
]

[[package]]
name = "databend-storages-common-pruner"
version = "0.1.0"
dependencies = [
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-storages-common-index",
 "databend-storages-common-table-meta",
 "log",
 "serde",
 "typetag",
]

[[package]]
name = "databend-storages-common-session"
version = "0.1.0"
dependencies = [
 "chrono",
 "databend-common-exception",
 "databend-common-meta-app",
 "databend-common-meta-types",
 "databend-common-storage",
 "databend-storages-common-blocks",
 "databend-storages-common-table-meta",
 "log",
 "parking_lot 0.12.3",
 "serde",
 "uuid",
]

[[package]]
name = "databend-storages-common-stage"
version = "0.1.0"
dependencies = [
 "databend-common-ast",
 "databend-common-catalog",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-meta-app",
 "serde",
 "typetag",
]

[[package]]
name = "databend-storages-common-table-meta"
version = "0.1.0"
dependencies = [
 "arrow",
 "bincode 1.3.3",
 "bytes",
 "chrono",
 "databend-common-base",
 "databend-common-column",
 "databend-common-datavalues",
 "databend-common-exception",
 "databend-common-expression",
 "databend-common-functions",
 "databend-common-io",
 "databend-common-native",
 "databend-common-storage",
 "enum-as-inner",
 "log",
 "parquet",
 "rmp-serde",
 "serde",
 "serde_json",
 "simple_hll",
 "snap",
 "typetag",
 "zstd 0.12.4",
]

[[package]]
name = "databend_educe"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f51ab98d2a6fa98f75221037606717e4f41dba478beb790ce679850d9362139f"
dependencies = [
 "enum-ordinalize",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "deadpool"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb84100978c1c7b37f09ed3ce3e5f843af02c2a2c431bae5b19230dad2c1b490"
dependencies = [
 "async-trait",
 "deadpool-runtime",
 "num_cpus",
 "tokio",
]

[[package]]
name = "deadpool-runtime"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "092966b41edc516079bdf31ec78a2e0588d1d0c08f78b91d8307215928642b2b"

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "uuid",
]

[[package]]
name = "deepsize"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cdb987ec36f6bf7bfbea3f928b75590b736fc42af8e54d97592481351b2b96c"
dependencies = [
 "deepsize_derive",
]

[[package]]
name = "deepsize_derive"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "990101d41f3bc8c1a45641024377ee284ecc338e5ecf3ea0f0e236d897c72796"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "defer"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "930c7171c8df9fb1782bdf9b918ed9ed2d33d1d22300abb754f9085bc48bf8e8"

[[package]]
name = "deflate64"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da692b8d1080ea3045efaab14434d40468c3d8657e42abddfffca87b428f4c1b"

[[package]]
name = "delta_kernel"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c96f51383ba327a1403e6e3458f8fc979d09d7200af56fa32681619f6c760dee"
dependencies = [
 "arrow",
 "bytes",
 "chrono",
 "delta_kernel_derive",
 "futures",
 "indexmap 2.9.0",
 "itertools 0.14.0",
 "object_store",
 "parquet",
 "reqwest",
 "roaring",
 "rustc_version",
 "serde",
 "serde_json",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "url",
 "uuid",
 "z85",
]

[[package]]
name = "delta_kernel_derive"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7b49a2e67ebafbe644e36f251ee985f237bfb39e4ef1e312eb5876535bc449e"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "deltalake"
version = "0.26.1"
source = "git+https://github.com/delta-io/delta-rs?rev=9954bff#9954bff62fc46bfe63734eba7a78a27b90295755"
dependencies = [
 "deltalake-core",
]

[[package]]
name = "deltalake-core"
version = "0.26.0"
source = "git+https://github.com/delta-io/delta-rs?rev=9954bff#9954bff62fc46bfe63734eba7a78a27b90295755"
dependencies = [
 "arrow",
 "arrow-arith",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-ipc",
 "arrow-json",
 "arrow-ord",
 "arrow-row",
 "arrow-schema",
 "arrow-select",
 "async-trait",
 "bytes",
 "cfg-if",
 "chrono",
 "dashmap 6.1.0",
 "delta_kernel",
 "deltalake-derive",
 "either",
 "futures",
 "humantime",
 "indexmap 2.9.0",
 "itertools 0.14.0",
 "maplit",
 "num-bigint",
 "num-traits",
 "num_cpus",
 "object_store",
 "parking_lot 0.12.3",
 "parquet",
 "percent-encoding",
 "pin-project-lite",
 "rand 0.8.5",
 "regex",
 "roaring",
 "serde",
 "serde_json",
 "sqlparser 0.56.0",
 "strum 0.27.1",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "url",
 "urlencoding",
 "uuid",
 "z85",
]

[[package]]
name = "deltalake-derive"
version = "0.26.0"
source = "git+https://github.com/delta-io/delta-rs?rev=9954bff#9954bff62fc46bfe63734eba7a78a27b90295755"
dependencies = [
 "convert_case 0.8.0",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "der"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7c1832837b905bbfb5101e07cc24c8deddf52f93225eee6ead5f4d63d53ddcb"
dependencies = [
 "const-oid",
 "pem-rfc7468",
 "zeroize",
]

[[package]]
name = "deranged"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c9e6a11ca8224451684bc0d7d5a7adbf8f2fd6887261a1cfc3c0432f9d4068e"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive-visitor"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d47165df83b9707cbada3216607a5d66125b6a66906de0bc1216c0669767ca9e"
dependencies = [
 "derive-visitor-macros",
]

[[package]]
name = "derive-visitor-macros"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "427b39a85fecafea16b1a5f3f50437151022e35eb4fe038107f08adbf7f8def6"
dependencies = [
 "convert_case 0.4.0",
 "itertools 0.10.5",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "derive_arbitrary"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30542c1ad912e0e3d22a1935c290e12e8a29d704a420177a31faad4a601a0800"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "derive_builder"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "507dfb09ea8b7fa618fcf76e953f4f5e192547945816d5358edffe39f6f94947"
dependencies = [
 "derive_builder_macro",
]

[[package]]
name = "derive_builder_core"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d5bcf7b024d6835cfb3d473887cd966994907effbe9227e8c8219824d06c4e8"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "derive_builder_macro"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab63b0e2bf4d5928aff72e83a7dace85d7bba5fe12dcc3c5a572d78caffd3f3c"
dependencies = [
 "derive_builder_core",
 "syn 2.0.101",
]

[[package]]
name = "derive_more"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a9b99b9cbbe49445b21764dc0625032a89b145a2642e67603e1c936f5458d05"
dependencies = [
 "derive_more-impl",
]

[[package]]
name = "derive_more-impl"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb7330aeadfbe296029522e6c40f315320aba36fc43a5b3632f3795348f3bd22"
dependencies = [
 "convert_case 0.6.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "unicode-xid",
]

[[package]]
name = "diff"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56254986775e3233ffa9c4d7d3faaf6d36a2c09d30b20687e9f88bc8bafc16c8"

[[package]]
name = "difflib"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6184e33543162437515c2e2b48714794e37845ec9851711914eec9d308f6ebe8"

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "directories-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339ee130d97a610ea5a5872d2bbb130fdf68884ff09d3028b81bec8a1ac23bbc"
dependencies = [
 "cfg-if",
 "dirs-sys-next",
]

[[package]]
name = "dirs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"
dependencies = [
 "dirs-sys",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "display-more"
version = "0.2.0"
source = "git+https://github.com/databendlabs/display-more?tag=v0.2.0#8a3e8351a72b9c6ac30f6ac4f6948abfd2c8c0b3"
dependencies = [
 "chrono",
 "chrono-tz 0.8.6",
]

[[package]]
name = "displaydoc"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97369cbbc041bc366949bc74d34658d6cda5621039731c6310521892a3a20ae0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "divan"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a405457ec78b8fe08b0e32b4a3570ab5dff6dd16eb9e76a5ee0a9d9cbd898933"
dependencies = [
 "cfg-if",
 "clap",
 "condtype",
 "divan-macros",
 "libc",
 "regex-lite",
]

[[package]]
name = "divan-macros"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9556bc800956545d6420a640173e5ba7dfa82f38d3ea5a167eb555bc69ac3323"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "dlv-list"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "442039f5147480ba31067cb00ada1adae6892028e40e45fc5de7b7df6dcc1b5f"
dependencies = [
 "const-random",
]

[[package]]
name = "docker_credential"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d89dfcba45b4afad7450a99b39e751590463e45c04728cf555d36bb66940de8"
dependencies = [
 "base64 0.21.7",
 "serde",
 "serde_json",
]

[[package]]
name = "document-features"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95249b50c6c185bee49034bcb378a49dc2b5dff0be90ff6616d31d64febab05d"
dependencies = [
 "litrs",
]

[[package]]
name = "dotenvy"
version = "0.15.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aaf95b3e5c8f23aa320147307562d361db0ae0d51242340f558153b4eb2439b"

[[package]]
name = "downcast"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1435fa1053d8b2fbbe9be7e97eca7f33d37b28409959813daefc1446a14247f1"

[[package]]
name = "downcast-rs"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b325c5dbd37f80359721ad39aca5a29fb04c89279657cffdda8736d0c0b9d2"

[[package]]
name = "dtoa"
version = "1.0.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6add3b8cff394282be81f3fc1a0605db594ed69890078ca6e2cab1c408bcf04"

[[package]]
name = "dtparse"
version = "2.0.0"
source = "git+https://github.com/datafuse-extras/dtparse.git?rev=30e28ca#30e28ca916b3563d98495d00063fa896cdb1cafe"
dependencies = [
 "chrono",
 "lazy_static",
 "num-traits",
 "rust_decimal",
]

[[package]]
name = "dunce"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92773504d58c093f6de2459af4af33faa518c13451eb8f2b5698ed3d36e7c813"

[[package]]
name = "dyn-clone"
version = "1.0.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c7a8fb8a9fbf66c1f703fe16184d10ca0ee9d23be5b4436400408ba54a95005"

[[package]]
name = "earcutr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79127ed59a85d7687c409e9978547cffb7dc79675355ed22da6b66fd5f6ead01"
dependencies = [
 "itertools 0.11.0",
 "num-traits",
]

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der",
 "digest",
 "elliptic-curve",
 "rfc6979",
 "signature",
 "spki",
]

[[package]]
name = "ed25519-compact"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9b3460f44bea8cd47f45a0c70892f1eff856d97cd55358b2f73f663789f6190"
dependencies = [
 "ct-codecs",
 "getrandom 0.2.16",
]

[[package]]
name = "educe"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7bc049e1bd8cdeb31b68bbd586a9464ecf9f3944af3958a7a9d0f8b9799417"
dependencies = [
 "enum-ordinalize",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "either"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48c757948c5ede0e46177b7add2e67155f70e33c07fea8284df6576da70b3719"
dependencies = [
 "serde",
]

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct",
 "crypto-bigint",
 "digest",
 "ff",
 "generic-array",
 "group",
 "hkdf",
 "pem-rfc7468",
 "pkcs8",
 "rand_core 0.6.4",
 "sec1",
 "subtle",
 "zeroize",
]

[[package]]
name = "embedded-io"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef1a6892d9eef45c8fa6b9e0086428a2cca8491aca8f787c534a3d6d0bcb3ced"

[[package]]
name = "embedded-io"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edd0f118536f44f5ccd48bcb8b111bdc3de888b58c74639dfb034a357d0f206d"

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "encoding_rs"
version = "0.8.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75030f3c4f45dafd7586dd6780965a8c7e8e285a5ecb86713e63a79c5b2766f3"
dependencies = [
 "cfg-if",
]

[[package]]
name = "endian-type"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c34f04666d835ff5d62e058c3995147c06f42fe86ff053337632bca83e42702d"

[[package]]
name = "enquote"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06c36cb11dbde389f4096111698d8b567c0720e3452fd5ac3e6b4e47e1939932"
dependencies = [
 "thiserror 1.0.69",
]

[[package]]
name = "enum-as-inner"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1e6a265c649f3f5979b601d26f1d05ada116434c87741c9493cb56218f76cbc"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "enum-ordinalize"
version = "4.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea0dcfa4e54eeb516fe454635a95753ddd39acda650ce703031c6973e315dd5"
dependencies = [
 "enum-ordinalize-derive",
]

[[package]]
name = "enum-ordinalize-derive"
version = "4.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d28318a75d4aead5c4db25382e8ef717932d0346600cacae6357eb5941bc5ff"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "enum_dispatch"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa18ce2bc66555b3218614519ac839ddb759a7d6720732f979ef8d13be147ecd"
dependencies = [
 "once_cell",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "enumflags2"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba2f4b465f5318854c6f8dd686ede6c0a9dc67d4b1ac241cf0eb51521a309147"
dependencies = [
 "enumflags2_derive",
 "serde",
]

[[package]]
name = "enumflags2_derive"
version = "0.7.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc4caf64a58d7a6d65ab00639b046ff54399a39f5f2554728895ace4b297cd79"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "env_filter"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "186e05a59d4c50738528153b83b0b0194d3a29507dfec16eccd4b342903397d0"
dependencies = [
 "log",
 "regex",
]

[[package]]
name = "env_logger"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a19187fea3ac7e84da7dacf48de0c45d63c6a76f9490dae389aead16c243fce3"
dependencies = [
 "log",
 "regex",
]

[[package]]
name = "env_logger"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cd405aab171cb85d6735e5c8d9db038c17d3ca007a4d2c25f337935c3d90580"
dependencies = [
 "humantime",
 "is-terminal",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "env_logger"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c863f0904021b108aa8b2f55046443e6b1ebde8fd4a15c399893aae4fa069f"
dependencies = [
 "anstream",
 "anstyle",
 "env_filter",
 "jiff 0.2.13",
 "log",
]

[[package]]
name = "equator"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4711b213838dfee0117e3be6ac926007d7f433d7bbe33595975d4190cb07e6fc"
dependencies = [
 "equator-macro",
]

[[package]]
name = "equator-macro"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44f23cf4b44bfce11a86ace86f8a73ffdec849c9fd00a386a53d278bd9e81fb3"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "equivalent"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877a4ace8713b0bcf2a4e7eec82529c029f1d0619886d18145fea96c3ffe5c0f"

[[package]]
name = "erased-serde"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e004d887f51fcb9fef17317a2f3525c887d8aa3f4f50fed920816a688284a5b7"
dependencies = [
 "serde",
 "typeid",
]

[[package]]
name = "errno"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "976dd42dc7e85965fe702eb8164f21f450704bdde31faefd6471dba214cb594e"
dependencies = [
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "error-code"
version = "3.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dea2df4cf52843e0452895c455a1a2cfbb842a1e7329671acf418fdc53ed4c59"

[[package]]
name = "escape8259"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5692dd7b5a1978a5aeb0ce83b7655c58ca8efdcb79d21036ea249da95afec2c6"

[[package]]
name = "etcetera"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "136d1b5283a1ab77bd9257427ffd09d8667ced0570b6f938942bc7568ed5b943"
dependencies = [
 "cfg-if",
 "home",
 "windows-sys 0.48.0",
]

[[package]]
name = "ethnum"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0939f82868b77ef93ce3c3c3daf2b3c526b456741da5a1a4559e590965b6026b"
dependencies = [
 "serde",
]

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "5.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3492acde4c3fc54c845eaab3eed8bd00c7a7d881f78bfc801e43a93dec1331ae"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8be9f3dfaaffdae2972880079a491a1a8bb7cbed0b8dd7a347f668b4150a3b93"
dependencies = [
 "event-listener 5.4.0",
 "pin-project-lite",
]

[[package]]
name = "eyre"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cd915d99f24784cdc19fd37ef22b97e3ff0ae756c7e492e9fbfe897d61e2aec"
dependencies = [
 "indenter",
 "once_cell",
]

[[package]]
name = "fallible-iterator"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2acce4a10f12dc2fb14a218589d4f1f62ef011b2d0cc4b3cb1bba8e94da14649"

[[package]]
name = "fallible-streaming-iterator"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7360491ce676a36bf9bb3c56c1aa791658183a54d2744120f27285738d90465a"

[[package]]
name = "fast-float2"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8eb564c5c7423d25c886fb561d1e4ee69f72354d16918afa32c08811f6b6a55"

[[package]]
name = "fastant"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62bf7fa928ce0c4a43bd6e7d1235318fc32ac3a3dea06a2208c44e729449471a"
dependencies = [
 "small_ctor",
 "web-time",
]

[[package]]
name = "fastdivide"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9afc2bd4d5a73106dd53d10d73d3401c2f32730ba2c0b93ddb888a8983680471"

[[package]]
name = "faster-hex"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2a2b11eda1d40935b26cf18f6833c526845ae8c41e58d09af6adeb6f0269183"

[[package]]
name = "fastrace"
version = "0.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c5a9b2e56fac2bf32bca26fdc509f674d0f2bdd15404b629ccee9c642453bb7"
dependencies = [
 "fastant",
 "fastrace-macro",
 "parking_lot 0.12.3",
 "pin-project",
 "rand 0.9.1",
 "rtrb",
 "serde",
]

[[package]]
name = "fastrace-macro"
version = "0.7.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9cdabd2b113942d0f771c11a7baf0edd24098b923ac546fd39b9811c82b4220"
dependencies = [
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "fastrace-opentelemetry"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b9ddbe3c21df04560ef1dfd29f426f7fb8ee1814e5c4b4260e7162c6c67d359"
dependencies = [
 "fastrace",
 "futures",
 "log",
 "opentelemetry",
 "opentelemetry_sdk",
]

[[package]]
name = "fastrand"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37909eebbb50d72f9059c3b6d82c0463f2ff062c9e95845c43a6c9c0355411be"

[[package]]
name = "faststr"
version = "0.2.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6503af7917fea18ffef8f7e8553fb8dff89e2e6837e94e09dd7fb069c82d62c"
dependencies = [
 "bytes",
 "rkyv 0.8.10",
 "serde",
 "simdutf8",
]

[[package]]
name = "fd-lock"
version = "4.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce92ff622d6dadf7349484f42c93271a0d49b7cc4d466a936405bacbe10aa78"
dependencies = [
 "cfg-if",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "feature-set"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2356b175ec39f95aed6c1b9f0ec5d44ae55aea73af722e25dd2323b2ef75f6c5"
dependencies = [
 "chrono",
 "semver",
]

[[package]]
name = "ff"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0b50bfb653653f9ca9095b427bed08ab8d75a137839d9ad64eb11810d5b6393"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "filetime"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35c0522e981e68cbfa8c3f978441a5f34b30b96e146b33cd3359176b50fe8586"
dependencies = [
 "cfg-if",
 "libc",
 "libredox",
 "windows-sys 0.59.0",
]

[[package]]
name = "findshlibs"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40b9e59cd0f7e0806cca4be089683ecb6434e602038df21fe6bf6711b2f07f64"
dependencies = [
 "cc",
 "lazy_static",
 "libc",
 "winapi",
]

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "fixedbitset"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d674e81391d1e1ab681a28d99df07927c6d4aa5b027d7da16ba32d1d21ecd99"

[[package]]
name = "flatbuffers"
version = "25.2.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1045398c1bfd89168b5fd3f1fc11f6e70b34f6f66300c87d44d3de849463abf1"
dependencies = [
 "bitflags 2.9.0",
 "rustc_version",
]

[[package]]
name = "flate2"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ced92e76e966ca2fd84c8f7aa01a4aea65b0eb6648d72f7c8f3e2764a67fece"
dependencies = [
 "crc32fast",
 "libz-rs-sys",
 "libz-sys",
 "miniz_oxide 0.8.8",
]

[[package]]
name = "float-cmp"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98de4bbd547a563b716d8dfa9aad1cb19bfab00f4fa09a6a4ed21dbcf44ce9c4"
dependencies = [
 "num-traits",
]

[[package]]
name = "float_eq"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28a80e3145d8ad11ba0995949bbcf48b9df2be62772b3d351ef017dff6ecb853"

[[package]]
name = "float_next_after"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bf7cc16383c4b8d58b9905a8509f02926ce3058053c056376248d958c9df1e8"

[[package]]
name = "flume"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da0e4dd2a88388a1f4ccc7c9ce104604dab68d9f408dc34cd45823d5a9069095"
dependencies = [
 "futures-core",
 "futures-sink",
 "spin 0.9.8",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foldhash"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9c4f5dac5e15c24eb999c26181a6ca40b39fe946cbe4c263c7209467bc83af2"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "foreign_vec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee1b05cbd864bcaecbd3455d6d967862d446e4ebfc3c2e5e5b9841e53cba6673"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "fragile"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28dd6caf6059519a65843af8fe2a3ae298b14b80179855aeb4adc2c1934ee619"

[[package]]
name = "frunk"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "874b6a17738fc273ec753618bac60ddaeac48cb1d7684c3e7bd472e57a28b817"
dependencies = [
 "frunk_core",
 "frunk_derives",
 "frunk_proc_macros",
 "serde",
]

[[package]]
name = "frunk_core"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3529a07095650187788833d585c219761114005d5976185760cf794d265b6a5c"
dependencies = [
 "serde",
]

[[package]]
name = "frunk_derives"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e99b8b3c28ae0e84b604c75f721c21dc77afb3706076af5e8216d15fd1deaae3"
dependencies = [
 "frunk_proc_macro_helpers",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "frunk_proc_macro_helpers"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05a956ef36c377977e512e227dcad20f68c2786ac7a54dacece3746046fea5ce"
dependencies = [
 "frunk_core",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "frunk_proc_macros"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67e86c2c9183662713fea27ea527aad20fb15fee635a71081ff91bf93df4dc51"
dependencies = [
 "frunk_core",
 "frunk_proc_macro_helpers",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "fs-err"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f89bda4c2a21204059a977ed3bfe746677dfd137b83c339e702b0ac91d482aa"
dependencies = [
 "autocfg",
]

[[package]]
name = "fs-set-times"
version = "0.20.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94e7099f6313ecacbe1256e8ff9d617b75d1bcb16a6fddef94866d225a01a14a"
dependencies = [
 "io-lifetimes",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "fs4"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7e180ac76c23b45e767bd7ae9579bc0bb458618c4bc71835926e098e61d15f8"
dependencies = [
 "rustix 0.38.44",
 "windows-sys 0.52.0",
]

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "fsevent-sys"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76ee7a02da4d231650c7cea31349b889be2f45ddb3ef3032d2ec8185f6313fd2"
dependencies = [
 "libc",
]

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65bc07b1a8bc7c85c5f2e110c476c7389b4554ba72af57d8445ea63a576b0876"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-async-stream"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38c4ff593faf24530b9a6eeea6ea4b5e3c20591da3f6348069724cb261d6d643"
dependencies = [
 "futures-async-stream-macro",
 "futures-core",
 "pin-project",
]

[[package]]
name = "futures-async-stream-macro"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f664c1c2186b81f798ac765d661fb8cefd74fdb398fd23c76c3fb3c1aec760e8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "futures-channel"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2dff15bf788c671c1934e366d07e30c1814a8ef514e1af724a602e8a2fbe1b10"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f29059c0c2090612e8d742178b0580d2dc940c837851ad723096f87af6663e"

[[package]]
name = "futures-executor"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e28d1d997f585e54aebc3f97d39e72338912123a67330d723fdbb564d646c9f"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-intrusive"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d930c203dd0b6ff06e0201a4a2fe9149b43c684fd4420555b26d21b1a02956f"
dependencies = [
 "futures-core",
 "lock_api",
 "parking_lot 0.12.3",
]

[[package]]
name = "futures-io"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e5c1b78ca4aae1ac06c48a526a655760685149f0d465d21f37abfe57ce075c6"

[[package]]
name = "futures-lite"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5edaec856126859abb19ed65f39e90fea3a9574b9707f13539acf4abf7eb532"
dependencies = [
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "futures-macro"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "162ee34ebcb7c64a8abebc059ce0fee27c2262618d7b60ed8faf72fef13c3650"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "futures-sink"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e575fab7d1e0dcb8d0c7bcf9a63ee213816ab51902e6d244a95819acacf1d4f7"

[[package]]
name = "futures-task"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f90f7dce0722e95104fcb095585910c0977252f286e354b5e3bd38902cd99988"

[[package]]
name = "futures-util"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fa08315bb612088cc391249efdc3bc77536f16c91f6cf495e6fbe85b20a4a81"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "fxprof-processed-profile"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "27d12c0aed7f1e24276a241aadc4cb8ea9f83000f34bc062b7cc2d51e3b0fabd"
dependencies = [
 "bitflags 2.9.0",
 "debugid",
 "fxhash",
 "serde",
 "serde_json",
]

[[package]]
name = "genawaiter2"
version = "0.100.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a954504d991886b6891c97b37ca1bf87ad2174c2126d52cdb52bebea7b1c89e3"
dependencies = [
 "genawaiter2-macro",
 "genawaiter2-proc-macro",
]

[[package]]
name = "genawaiter2-macro"
version = "0.100.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5cc2b187655aae60ff27f3978ffd192b9252c1a5905f53d3340e5836d8a539b"

[[package]]
name = "genawaiter2-proc-macro"
version = "0.100.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91467b1ef9d0b102db5ebf041238f26879f49ac323459e16725991ef73e61d2d"
dependencies = [
 "proc-macro-error 0.4.12",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "generator"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5cc16584ff22b460a382b7feec54b23d2908d858152e5739a120b949293bd74e"
dependencies = [
 "cc",
 "libc",
 "log",
 "rustversion",
 "windows 0.48.0",
]

[[package]]
name = "generator"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d18470a76cb7f8ff746cf1f7470914f900252ec36bbc40b569d74b1258446827"
dependencies = [
 "cc",
 "cfg-if",
 "libc",
 "log",
 "rustversion",
 "windows 0.61.1",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "geo"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f811f663912a69249fa620dcd2a005db7254529da2d8a0b23942e81f47084501"
dependencies = [
 "earcutr",
 "float_next_after",
 "geo-types",
 "geographiclib-rs",
 "log",
 "num-traits",
 "robust",
 "rstar",
 "serde",
 "spade",
]

[[package]]
name = "geo-types"
version = "0.7.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ff16065e5720f376fbced200a5ae0f47ace85fd70b7e54269790281353b6d61"
dependencies = [
 "approx",
 "num-traits",
 "rstar",
 "serde",
]

[[package]]
name = "geographiclib-rs"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6e5ed84f8089c70234b0a8e0aedb6dc733671612ddc0d37c6066052f9781960"
dependencies = [
 "libm",
]

[[package]]
name = "geohash"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fb94b1a65401d6cbf22958a9040aa364812c26674f841bee538b12c135db1e6"
dependencies = [
 "geo-types",
 "libm",
]

[[package]]
name = "geojson"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5d728c1df1fbf328d74151efe6cb0586f79ee813346ea981add69bd22c9241b"
dependencies = [
 "log",
 "serde",
 "serde_json",
 "thiserror 1.0.69",
]

[[package]]
name = "geozero"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5f28f34864745eb2f123c990c6ffd92c1584bd39439b3f27ff2a0f4ea5b309b"
dependencies = [
 "geo-types",
 "geojson",
 "log",
 "scroll 0.11.0",
 "serde_json",
 "thiserror 1.0.69",
 "wkt",
]

[[package]]
name = "getopts"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14dbbfd5c71d70241ecf9e6f13737f7b5ce823821063188d7e46c41d371eebd5"
dependencies = [
 "unicode-width 0.1.14",
]

[[package]]
name = "getrandom"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "335ff9f135e4384c8150d6f27c6daed433577f86b4750418338c01a1a2528592"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "getrandom"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26145e563e54f2cadc477553f1ec5ee650b00862f0a58bcd12cbdc5f0ea2d2f4"
dependencies = [
 "cfg-if",
 "js-sys",
 "libc",
 "r-efi",
 "wasi 0.14.2+wasi-0.2.4",
 "wasm-bindgen",
]

[[package]]
name = "ghash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0d8a4362ccb29cb0b265253fb0a2728f592895ee6854fd9bc13f2ffda266ff1"
dependencies = [
 "opaque-debug",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40ecd4077b5ae9fd2e9e169b102c6c330d0605168eb0e8bf79952b256dbefffd"

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"
dependencies = [
 "fallible-iterator",
 "indexmap 2.9.0",
 "stable_deref_trait",
]

[[package]]
name = "gix"
version = "0.63.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "984c5018adfa7a4536ade67990b3ebc6e11ab57b3d6cd9968de0947ca99b4b06"
dependencies = [
 "gix-actor",
 "gix-archive",
 "gix-attributes",
 "gix-command",
 "gix-commitgraph",
 "gix-config",
 "gix-credentials",
 "gix-date",
 "gix-diff",
 "gix-dir",
 "gix-discover",
 "gix-features",
 "gix-filter",
 "gix-fs",
 "gix-glob",
 "gix-hash",
 "gix-hashtable",
 "gix-ignore",
 "gix-index",
 "gix-lock",
 "gix-macros",
 "gix-mailmap",
 "gix-negotiate",
 "gix-object",
 "gix-odb",
 "gix-pack",
 "gix-path",
 "gix-pathspec",
 "gix-prompt",
 "gix-ref",
 "gix-refspec",
 "gix-revision",
 "gix-revwalk",
 "gix-sec",
 "gix-status",
 "gix-submodule",
 "gix-tempfile",
 "gix-trace",
 "gix-traverse",
 "gix-url",
 "gix-utils",
 "gix-validate 0.8.5",
 "gix-worktree",
 "gix-worktree-state",
 "gix-worktree-stream",
 "once_cell",
 "parking_lot 0.12.3",
 "regex",
 "signal-hook",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-actor"
version = "0.31.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0e454357e34b833cc3a00b6efbbd3dd4d18b24b9fb0c023876ec2645e8aa3f2"
dependencies = [
 "bstr",
 "gix-date",
 "gix-utils",
 "itoa",
 "thiserror 1.0.69",
 "winnow 0.6.26",
]

[[package]]
name = "gix-archive"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63b6bbebdf0223d1d4a69d6027e8b2482daad8eb1a8d3ec97176c7ec58e796d4"
dependencies = [
 "bstr",
 "gix-date",
 "gix-object",
 "gix-worktree-stream",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-attributes"
version = "0.22.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebccbf25aa4a973dd352564a9000af69edca90623e8a16dad9cbc03713131311"
dependencies = [
 "bstr",
 "gix-glob",
 "gix-path",
 "gix-quote",
 "gix-trace",
 "kstring",
 "smallvec",
 "thiserror 1.0.69",
 "unicode-bom",
]

[[package]]
name = "gix-bitmap"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1db9765c69502650da68f0804e3dc2b5f8ccc6a2d104ca6c85bc40700d37540"
dependencies = [
 "thiserror 2.0.12",
]

[[package]]
name = "gix-chunk"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b1f1d8764958699dc764e3f727cef280ff4d1bd92c107bbf8acd85b30c1bd6f"
dependencies = [
 "thiserror 2.0.12",
]

[[package]]
name = "gix-command"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d7d6b8f3a64453fd7e8191eb80b351eb7ac0839b40a1237cd2c137d5079fe53"
dependencies = [
 "bstr",
 "gix-path",
 "gix-trace",
 "shell-words",
]

[[package]]
name = "gix-commitgraph"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "133b06f67f565836ec0c473e2116a60fb74f80b6435e21d88013ac0e3c60fc78"
dependencies = [
 "bstr",
 "gix-chunk",
 "gix-features",
 "gix-hash",
 "memmap2",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-config"
version = "0.37.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53fafe42957e11d98e354a66b6bd70aeea00faf2f62dd11164188224a507c840"
dependencies = [
 "bstr",
 "gix-config-value",
 "gix-features",
 "gix-glob",
 "gix-path",
 "gix-ref",
 "gix-sec",
 "memchr",
 "once_cell",
 "smallvec",
 "thiserror 1.0.69",
 "unicode-bom",
 "winnow 0.6.26",
]

[[package]]
name = "gix-config-value"
version = "0.14.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dc2c844c4cf141884678cabef736fd91dd73068b9146e6f004ba1a0457944b6"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-path",
 "libc",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-credentials"
version = "0.24.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce391d305968782f1ae301c4a3d42c5701df7ff1d8bc03740300f6fd12bce78"
dependencies = [
 "bstr",
 "gix-command",
 "gix-config-value",
 "gix-path",
 "gix-prompt",
 "gix-sec",
 "gix-trace",
 "gix-url",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-date"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9eed6931f21491ee0aeb922751bd7ec97b4b2fe8fbfedcb678e2a2dce5f3b8c0"
dependencies = [
 "bstr",
 "itoa",
 "thiserror 1.0.69",
 "time",
]

[[package]]
name = "gix-diff"
version = "0.44.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1996d5c8a305b59709467d80617c9fde48d9d75fd1f4179ea970912630886c9d"
dependencies = [
 "bstr",
 "gix-command",
 "gix-filter",
 "gix-fs",
 "gix-hash",
 "gix-object",
 "gix-path",
 "gix-tempfile",
 "gix-trace",
 "gix-worktree",
 "imara-diff",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-dir"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60c99f8c545abd63abe541d20ab6cda347de406c0a3f1c80aadc12d9b0e94974"
dependencies = [
 "bstr",
 "gix-discover",
 "gix-fs",
 "gix-ignore",
 "gix-index",
 "gix-object",
 "gix-path",
 "gix-pathspec",
 "gix-trace",
 "gix-utils",
 "gix-worktree",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-discover"
version = "0.32.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc27c699b63da66b50d50c00668bc0b7e90c3a382ef302865e891559935f3dbf"
dependencies = [
 "bstr",
 "dunce",
 "gix-fs",
 "gix-hash",
 "gix-path",
 "gix-ref",
 "gix-sec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-features"
version = "0.38.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac7045ac9fe5f9c727f38799d002a7ed3583cd777e3322a7c4b43e3cf437dc69"
dependencies = [
 "bytes",
 "bytesize",
 "crc32fast",
 "crossbeam-channel",
 "flate2",
 "gix-hash",
 "gix-trace",
 "gix-utils",
 "jwalk",
 "libc",
 "once_cell",
 "parking_lot 0.12.3",
 "prodash",
 "sha1_smol",
 "thiserror 1.0.69",
 "walkdir",
]

[[package]]
name = "gix-filter"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6547738da28275f4dff4e9f3a0f28509f53f94dd6bd822733c91cb306bca61a"
dependencies = [
 "bstr",
 "encoding_rs",
 "gix-attributes",
 "gix-command",
 "gix-hash",
 "gix-object",
 "gix-packetline-blocking",
 "gix-path",
 "gix-quote",
 "gix-trace",
 "gix-utils",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-fs"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2bfe6249cfea6d0c0e0990d5226a4cb36f030444ba9e35e0639275db8f98575"
dependencies = [
 "fastrand",
 "gix-features",
 "gix-utils",
]

[[package]]
name = "gix-glob"
version = "0.16.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74908b4bbc0a0a40852737e5d7889f676f081e340d5451a16e5b4c50d592f111"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-features",
 "gix-path",
]

[[package]]
name = "gix-hash"
version = "0.14.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f93d7df7366121b5018f947a04d37f034717e113dcf9ccd85c34b58e57a74d5e"
dependencies = [
 "faster-hex",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-hashtable"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ddf80e16f3c19ac06ce415a38b8591993d3f73aede049cb561becb5b3a8e242"
dependencies = [
 "gix-hash",
 "hashbrown 0.14.5",
 "parking_lot 0.12.3",
]

[[package]]
name = "gix-ignore"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e447cd96598460f5906a0f6c75e950a39f98c2705fc755ad2f2020c9e937fab7"
dependencies = [
 "bstr",
 "gix-glob",
 "gix-path",
 "gix-trace",
 "unicode-bom",
]

[[package]]
name = "gix-index"
version = "0.33.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a9a44eb55bd84bb48f8a44980e951968ced21e171b22d115d1cdcef82a7d73f"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "filetime",
 "fnv",
 "gix-bitmap",
 "gix-features",
 "gix-fs",
 "gix-hash",
 "gix-lock",
 "gix-object",
 "gix-traverse",
 "gix-utils",
 "gix-validate 0.8.5",
 "hashbrown 0.14.5",
 "itoa",
 "libc",
 "memmap2",
 "rustix 0.38.44",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-lock"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bc7fe297f1f4614774989c00ec8b1add59571dc9b024b4c00acb7dedd4e19d"
dependencies = [
 "gix-tempfile",
 "gix-utils",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-macros"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "999ce923619f88194171a67fb3e6d613653b8d4d6078b529b15a765da0edcc17"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "gix-mailmap"
version = "0.23.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef6daca6edb6a590c7c0533f3f8e75c54663eb56ce08f46f0891db9fc6f09208"
dependencies = [
 "bstr",
 "gix-actor",
 "gix-date",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-negotiate"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ec879fb6307bb63519ba89be0024c6f61b4b9d61f1a91fd2ce572d89fe9c224"
dependencies = [
 "bitflags 2.9.0",
 "gix-commitgraph",
 "gix-date",
 "gix-hash",
 "gix-object",
 "gix-revwalk",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-object"
version = "0.42.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25da2f46b4e7c2fa7b413ce4dffb87f69eaf89c2057e386491f4c55cadbfe386"
dependencies = [
 "bstr",
 "gix-actor",
 "gix-date",
 "gix-features",
 "gix-hash",
 "gix-utils",
 "gix-validate 0.8.5",
 "itoa",
 "smallvec",
 "thiserror 1.0.69",
 "winnow 0.6.26",
]

[[package]]
name = "gix-odb"
version = "0.61.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20d384fe541d93d8a3bb7d5d5ef210780d6df4f50c4e684ccba32665a5e3bc9b"
dependencies = [
 "arc-swap",
 "gix-date",
 "gix-features",
 "gix-fs",
 "gix-hash",
 "gix-object",
 "gix-pack",
 "gix-path",
 "gix-quote",
 "parking_lot 0.12.3",
 "tempfile",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-pack"
version = "0.51.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e0594491fffe55df94ba1c111a6566b7f56b3f8d2e1efc750e77d572f5f5229"
dependencies = [
 "clru",
 "gix-chunk",
 "gix-features",
 "gix-hash",
 "gix-hashtable",
 "gix-object",
 "gix-path",
 "memmap2",
 "smallvec",
 "thiserror 1.0.69",
 "uluru",
]

[[package]]
name = "gix-packetline-blocking"
version = "0.17.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9802304baa798dd6f5ff8008a2b6516d54b74a69ca2d3a2b9e2d6c3b5556b40"
dependencies = [
 "bstr",
 "faster-hex",
 "gix-trace",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-path"
version = "0.10.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567f65fec4ef10dfab97ae71f26a27fd4d7fe7b8e3f90c8a58551c41ff3fb65b"
dependencies = [
 "bstr",
 "gix-trace",
 "gix-validate 0.10.0",
 "home",
 "once_cell",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-pathspec"
version = "0.7.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d23bf239532b4414d0e63b8ab3a65481881f7237ed9647bb10c1e3cc54c5ceb"
dependencies = [
 "bitflags 2.9.0",
 "bstr",
 "gix-attributes",
 "gix-config-value",
 "gix-glob",
 "gix-path",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-prompt"
version = "0.8.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a7822afc4bc9c5fbbc6ce80b00f41c129306b7685cac3248dbfa14784960594"
dependencies = [
 "gix-command",
 "gix-config-value",
 "parking_lot 0.12.3",
 "rustix 0.38.44",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-quote"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e49357fccdb0c85c0d3a3292a9f6db32d9b3535959b5471bb9624908f4a066c6"
dependencies = [
 "bstr",
 "gix-utils",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-ref"
version = "0.44.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3394a2997e5bc6b22ebc1e1a87b41eeefbcfcff3dbfa7c4bd73cb0ac8f1f3e2e"
dependencies = [
 "gix-actor",
 "gix-date",
 "gix-features",
 "gix-fs",
 "gix-hash",
 "gix-lock",
 "gix-object",
 "gix-path",
 "gix-tempfile",
 "gix-utils",
 "gix-validate 0.8.5",
 "memmap2",
 "thiserror 1.0.69",
 "winnow 0.6.26",
]

[[package]]
name = "gix-refspec"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6868f8cd2e62555d1f7c78b784bece43ace40dd2a462daf3b588d5416e603f37"
dependencies = [
 "bstr",
 "gix-hash",
 "gix-revision",
 "gix-validate 0.8.5",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-revision"
version = "0.27.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01b13e43c2118c4b0537ddac7d0821ae0dfa90b7b8dbf20c711e153fb749adce"
dependencies = [
 "bstr",
 "gix-date",
 "gix-hash",
 "gix-hashtable",
 "gix-object",
 "gix-revwalk",
 "gix-trace",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-revwalk"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b030ccaab71af141f537e0225f19b9e74f25fefdba0372246b844491cab43e0"
dependencies = [
 "gix-commitgraph",
 "gix-date",
 "gix-hash",
 "gix-hashtable",
 "gix-object",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-sec"
version = "0.10.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47aeb0f13de9ef2f3033f5ff218de30f44db827ac9f1286f9ef050aacddd5888"
dependencies = [
 "bitflags 2.9.0",
 "gix-path",
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "gix-status"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f4373d989713809554d136f51bc7da565adf45c91aa4d86ef6a79801621bfc8"
dependencies = [
 "bstr",
 "filetime",
 "gix-diff",
 "gix-dir",
 "gix-features",
 "gix-filter",
 "gix-fs",
 "gix-hash",
 "gix-index",
 "gix-object",
 "gix-path",
 "gix-pathspec",
 "gix-worktree",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-submodule"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "921cd49924ac14b6611b22e5fb7bbba74d8780dc7ad26153304b64d1272460ac"
dependencies = [
 "bstr",
 "gix-config",
 "gix-path",
 "gix-pathspec",
 "gix-refspec",
 "gix-url",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-tempfile"
version = "14.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "046b4927969fa816a150a0cda2e62c80016fe11fb3c3184e4dddf4e542f108aa"
dependencies = [
 "dashmap 6.1.0",
 "gix-fs",
 "libc",
 "once_cell",
 "parking_lot 0.12.3",
 "signal-hook",
 "signal-hook-registry",
 "tempfile",
]

[[package]]
name = "gix-trace"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c396a2036920c69695f760a65e7f2677267ccf483f25046977d87e4cb2665f7"

[[package]]
name = "gix-traverse"
version = "0.39.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e499a18c511e71cf4a20413b743b9f5bcf64b3d9e81e9c3c6cd399eae55a8840"
dependencies = [
 "bitflags 2.9.0",
 "gix-commitgraph",
 "gix-date",
 "gix-hash",
 "gix-hashtable",
 "gix-object",
 "gix-revwalk",
 "smallvec",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-url"
version = "0.27.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd280c5e84fb22e128ed2a053a0daeacb6379469be6a85e3d518a0636e160c89"
dependencies = [
 "bstr",
 "gix-features",
 "gix-path",
 "home",
 "thiserror 1.0.69",
 "url",
]

[[package]]
name = "gix-utils"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff08f24e03ac8916c478c8419d7d3c33393da9bb41fa4c24455d5406aeefd35f"
dependencies = [
 "bstr",
 "fastrand",
 "unicode-normalization",
]

[[package]]
name = "gix-validate"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82c27dd34a49b1addf193c92070bcbf3beaf6e10f16a78544de6372e146a0acf"
dependencies = [
 "bstr",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-validate"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77b9e00cacde5b51388d28ed746c493b18a6add1f19b5e01d686b3b9ece66d4d"
dependencies = [
 "bstr",
 "thiserror 2.0.12",
]

[[package]]
name = "gix-worktree"
version = "0.34.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26f7326ebe0b9172220694ea69d344c536009a9b98fb0f9de092c440f3efe7a6"
dependencies = [
 "bstr",
 "gix-attributes",
 "gix-features",
 "gix-fs",
 "gix-glob",
 "gix-hash",
 "gix-ignore",
 "gix-index",
 "gix-object",
 "gix-path",
 "gix-validate 0.8.5",
]

[[package]]
name = "gix-worktree-state"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39ed6205b5f51067a485b11843babcf3304c0799e265a06eb0dde7f69cd85cd8"
dependencies = [
 "bstr",
 "gix-features",
 "gix-filter",
 "gix-fs",
 "gix-glob",
 "gix-hash",
 "gix-index",
 "gix-object",
 "gix-path",
 "gix-worktree",
 "io-close",
 "thiserror 1.0.69",
]

[[package]]
name = "gix-worktree-stream"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e35d4896249a41856f44571d94d7583b9f3b9cd1a75eaef4f34a4aa2981bed21"
dependencies = [
 "gix-attributes",
 "gix-features",
 "gix-filter",
 "gix-fs",
 "gix-hash",
 "gix-object",
 "gix-path",
 "gix-traverse",
 "parking_lot 0.12.3",
 "thiserror 1.0.69",
]

[[package]]
name = "glob"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8d1add55171497b4705a648c6b583acafb01d58050a51727785f0b2c8e0a2b2"

[[package]]
name = "globiter"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0757cd38a6dc71ff622f52309d213d1b3a39297f49db6aaf6a820f9487c7cb34"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
]

[[package]]
name = "gloo-timers"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb143cf96099802033e0d4f4963b19fd2e0b728bcf076cd9cf7f6634f092994"
dependencies = [
 "futures-channel",
 "futures-core",
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "goldenfile"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf39e208efa110ca273f7255aea02485103ffcb7e5dfa5e4196b05a02411618e"
dependencies = [
 "scopeguard",
 "similar-asserts",
 "tempfile",
 "yansi",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "h2"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81fe527a889e1532da5c525686d96d4c2e74cdd345badf8dfef9f6b39dd5f5e8"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http 0.2.12",
 "indexmap 2.9.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "h2"
version = "0.4.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9421a676d1b147b16b82c9225157dc629087ef8ec4d5e2960f9437a90dac0a5"
dependencies = [
 "atomic-waker",
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "http 1.3.1",
 "indexmap 2.9.0",
 "slab",
 "tokio",
 "tokio-util",
 "tracing",
]

[[package]]
name = "h3o"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6fb938100651db317719f46877a3cd82105920be4ea2ff49d55d1d65fa7bec1"
dependencies = [
 "ahash 0.8.12",
 "auto_ops",
 "either",
 "float_eq",
 "konst",
]

[[package]]
name = "half"
version = "2.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "459196ed295495a68f7d7fe1d84f6c4b7ff0e21fe3017b2f283c6fac3ad803c9"
dependencies = [
 "cfg-if",
 "crunchy",
 "num-traits",
]

[[package]]
name = "hash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "47d60b12902ba28e2730cd37e95b8c9223af2808df9e902d4df49588d1470606"
dependencies = [
 "byteorder",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.14.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1"
dependencies = [
 "ahash 0.8.12",
 "allocator-api2",
 "serde",
]

[[package]]
name = "hashbrown"
version = "0.15.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84b26c544d002229e640969970a2e74021aadf6e2f96372b9c58eff97de08eb3"
dependencies = [
 "allocator-api2",
 "equivalent",
 "foldhash",
]

[[package]]
name = "hashlink"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8094feaf31ff591f651a2664fb9cfd92bba7a60ce3197265e9482ebe753c8f7"
dependencies = [
 "hashbrown 0.14.5",
]

[[package]]
name = "hashlink"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7382cf6263419f2d8df38c55d7da83da5c18aef87fc7a7fc1fb1e344edfe14c1"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "hdfs-sys"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33e2d5cefba2d51a26b44d2a493f963a32725a0f6593c91be4a610ad449c49cb"
dependencies = [
 "cc",
 "java-locator",
]

[[package]]
name = "hdrs"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7c42a693bfe5dc8fcad1f24044c5ec355c5f157b8ce63c7d62f51cecbc7878d"
dependencies = [
 "blocking",
 "errno",
 "futures",
 "hdfs-sys",
 "libc",
 "log",
]

[[package]]
name = "headers"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "322106e6bd0cba2d5ead589ddb8150a13d7c4217cf80d7c4f682ca994ccc6aa9"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "headers-core",
 "http 1.3.1",
 "httpdate",
 "mime",
 "sha1",
]

[[package]]
name = "headers-core"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54b4a22553d4242c49fddb9ba998a99962b5cc6f22cb5a3482bec22522403ce4"
dependencies = [
 "http 1.3.1",
]

[[package]]
name = "heapless"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bfb9eb618601c89945a70e254898da93b13be0388091d42117462b265bb3fad"
dependencies = [
 "hash32",
 "stable_deref_trait",
]

[[package]]
name = "heck"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d621efb26863f0e9924c6ac577e8275e5e6b77455db64ffa6c65c904e9e132c"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.1.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62b467343b94ba476dcb2500d242dadbb39557df889310ac77c5d99100aaac33"
dependencies = [
 "libc",
]

[[package]]
name = "hermit-abi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231dfb89cfffdbc30e7fc41579ed6066ad03abda9e567ccafae602b97ec5024"

[[package]]
name = "hermit-abi"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f154ce46856750ed433c8649605bf7ed2de3bc35fd9d2a9f30cddd873c80cb08"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"

[[package]]
name = "hickory-proto"
version = "0.25.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8a6fe56c0038198998a6f217ca4e7ef3a5e51f46163bd6dd60b5c71ca6c6502"
dependencies = [
 "async-trait",
 "cfg-if",
 "data-encoding",
 "enum-as-inner",
 "futures-channel",
 "futures-io",
 "futures-util",
 "idna",
 "ipnet",
 "once_cell",
 "rand 0.9.1",
 "ring",
 "thiserror 2.0.12",
 "tinyvec",
 "tokio",
 "tracing",
 "url",
]

[[package]]
name = "hickory-resolver"
version = "0.25.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc62a9a99b0bfb44d2ab95a7208ac952d31060efc16241c87eaf36406fecf87a"
dependencies = [
 "cfg-if",
 "futures-util",
 "hickory-proto",
 "ipconfig",
 "moka",
 "once_cell",
 "parking_lot 0.12.3",
 "rand 0.9.1",
 "resolv-conf",
 "smallvec",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
]

[[package]]
name = "hifijson"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9958ab3ce3170c061a27679916bd9b969eceeb5e8b120438e6751d0987655c42"

[[package]]
name = "hive_metastore"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35f502759a3b4517dc44d06d8cdaddb942b7930bb81ebf7d645e209cfc7b7e43"
dependencies = [
 "anyhow",
 "pilota",
 "volo",
 "volo-thrift",
]

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest",
]

[[package]]
name = "hmac-sha1-compact"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18492c9f6f9a560e0d346369b665ad2bdbc89fa9bceca75796584e79042694c3"

[[package]]
name = "hmac-sha256"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a8575493d277c9092b988c780c94737fb9fd8651a1001e16bee3eccfc1baedb"
dependencies = [
 "digest",
]

[[package]]
name = "hmac-sha512"
version = "1.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0b3a0f572aa8389d325f5852b9e0a333a15b0f86ecccbb3fdb6e97cd86dc67c"
dependencies = [
 "digest",
]

[[package]]
name = "home"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589533453244b0995c858700322199b2becb13b627df2851f64a2775d024abcf"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "hostname"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c731c3e10504cc8ed35cfe2f1db4c9274c3d35fa486e3b31df46f068ef3e867"
dependencies = [
 "libc",
 "match_cfg",
 "winapi",
]

[[package]]
name = "htmlescape"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9025058dae765dee5070ec375f591e2ba14638c63feff74f13805a72e523163"

[[package]]
name = "http"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "601cbb57e577e2f5ef5be8e7b83f0f63994f25aa94d673e54a92d5c516d101f1"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4a85d31aea989eead29a3aaf9e1115a180df8282431156e533de47660892565"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes",
 "http 0.2.12",
 "pin-project-lite",
]

[[package]]
name = "http-body"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1efedce1fb8e6913f23e0c92de8e62cd5b772a67e7b3946df930a62566c93184"
dependencies = [
 "bytes",
 "http 1.3.1",
]

[[package]]
name = "http-body-util"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b021d93e26becf5dc7e1b75b1bed1fd93124b374ceb73f43d4d4eafec896a64a"
dependencies = [
 "bytes",
 "futures-core",
 "http 1.3.1",
 "http-body 1.0.1",
 "pin-project-lite",
]

[[package]]
name = "httparse"
version = "1.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dbf3de79e51f3d586ab4cb9d5c3e2c14aa28ed23d180cf89b4df0454a69cc87"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "human_format"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c3b1f728c459d27b12448862017b96ad4767b1ec2ec5e6434e99f1577f085b8"

[[package]]
name = "humantime"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b112acc8b3adf4b107a8ec20977da0273a8c386765a3ec0229bd500a1443f9f"

[[package]]
name = "hyper"
version = "0.14.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41dfc780fdec9373c01bae43289ea34c972e40ee3c9f6b3c8801a35f35586ce7"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2 0.3.26",
 "http 0.2.12",
 "http-body 0.4.6",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc2b571658e38e0c01b1fdca3bbbe93c00d3d71693ff2770043f8c29bc7d6f80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "h2 0.4.10",
 "http 1.3.1",
 "http-body 1.0.1",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "smallvec",
 "tokio",
 "want",
]

[[package]]
name = "hyper-named-pipe"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73b7d8abf35697b81a825e386fc151e0d503e8cb5fcb93cc8669c376dfd6f278"
dependencies = [
 "hex",
 "hyper 1.6.0",
 "hyper-util",
 "pin-project-lite",
 "tokio",
 "tower-service",
 "winapi",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http 0.2.12",
 "hyper 0.14.32",
 "log",
 "rustls 0.21.12",
 "rustls-native-certs 0.6.3",
 "tokio",
 "tokio-rustls 0.24.1",
]

[[package]]
name = "hyper-rustls"
version = "0.27.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d191583f3da1305256f22463b9bb0471acad48a4e534a5218b9963e9c1f59b2"
dependencies = [
 "futures-util",
 "http 1.3.1",
 "hyper 1.6.0",
 "hyper-util",
 "rustls 0.23.27",
 "rustls-native-certs 0.8.1",
 "rustls-pki-types",
 "tokio",
 "tokio-rustls 0.26.2",
 "tower-service",
 "webpki-roots 0.26.11",
]

[[package]]
name = "hyper-timeout"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b90d566bffbce6a75bd8b09a05aa8c2cb1fabb6cb348f8840c9e4c90a0d83b0"
dependencies = [
 "hyper 1.6.0",
 "hyper-util",
 "pin-project-lite",
 "tokio",
 "tower-service",
]

[[package]]
name = "hyper-tls"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70206fc6890eaca9fde8a0bf71caa2ddfc9fe045ac9e5c70df101a7dbde866e0"
dependencies = [
 "bytes",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "native-tls",
 "tokio",
 "tokio-native-tls",
 "tower-service",
]

[[package]]
name = "hyper-util"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "497bbc33a26fdd4af9ed9c70d63f61cf56a938375fbb32df34db9b1cd6d643f2"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-util",
 "http 1.3.1",
 "http-body 1.0.1",
 "hyper 1.6.0",
 "libc",
 "pin-project-lite",
 "socket2",
 "tokio",
 "tower-service",
 "tracing",
]

[[package]]
name = "hyperlocal"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "986c5ce3b994526b3cd75578e62554abd09f0899d6206de48b3e96ab34ccc8c7"
dependencies = [
 "hex",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "pin-project-lite",
 "tokio",
 "tower-service",
]

[[package]]
name = "iana-time-zone"
version = "0.1.63"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0c919e5debc312ad217002b8048a17b7d83f80703865bbfcfebb0458b0b27d8"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "log",
 "wasm-bindgen",
 "windows-core 0.61.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "iceberg"
version = "0.4.0"
source = "git+https://github.com/databendlabs/iceberg-rust?rev=d5cca1c15f240f3cb04e57569bce648933b1c79b#d5cca1c15f240f3cb04e57569bce648933b1c79b"
dependencies = [
 "anyhow",
 "apache-avro",
 "array-init",
 "arrow-arith",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-ord",
 "arrow-schema",
 "arrow-select",
 "arrow-string",
 "async-trait",
 "bimap",
 "bytes",
 "chrono",
 "derive_builder",
 "fnv",
 "futures",
 "itertools 0.13.0",
 "moka",
 "murmur3",
 "num-bigint",
 "once_cell",
 "opendal",
 "ordered-float 4.6.0",
 "parquet",
 "rand 0.8.5",
 "reqwest",
 "roaring",
 "rust_decimal",
 "serde",
 "serde_bytes",
 "serde_derive",
 "serde_json",
 "serde_repr",
 "serde_with",
 "thrift",
 "tokio",
 "typed-builder 0.20.1",
 "url",
 "uuid",
 "zstd 0.13.3",
]

[[package]]
name = "iceberg-catalog-glue"
version = "0.4.0"
source = "git+https://github.com/databendlabs/iceberg-rust?rev=d5cca1c15f240f3cb04e57569bce648933b1c79b#d5cca1c15f240f3cb04e57569bce648933b1c79b"
dependencies = [
 "anyhow",
 "async-trait",
 "aws-config",
 "aws-sdk-glue",
 "iceberg",
 "serde_json",
 "tokio",
 "tracing",
 "typed-builder 0.20.1",
 "uuid",
]

[[package]]
name = "iceberg-catalog-hms"
version = "0.4.0"
source = "git+https://github.com/databendlabs/iceberg-rust?rev=d5cca1c15f240f3cb04e57569bce648933b1c79b#d5cca1c15f240f3cb04e57569bce648933b1c79b"
dependencies = [
 "anyhow",
 "async-trait",
 "chrono",
 "faststr",
 "hive_metastore",
 "iceberg",
 "linkedbytes",
 "metainfo",
 "motore-macros",
 "pilota",
 "serde_json",
 "tokio",
 "tracing",
 "typed-builder 0.20.1",
 "uuid",
 "volo",
 "volo-thrift",
]

[[package]]
name = "iceberg-catalog-rest"
version = "0.4.0"
source = "git+https://github.com/databendlabs/iceberg-rust?rev=d5cca1c15f240f3cb04e57569bce648933b1c79b#d5cca1c15f240f3cb04e57569bce648933b1c79b"
dependencies = [
 "async-trait",
 "chrono",
 "http 1.3.1",
 "iceberg",
 "itertools 0.13.0",
 "reqwest",
 "serde",
 "serde_derive",
 "serde_json",
 "tokio",
 "tracing",
 "typed-builder 0.20.1",
 "uuid",
]

[[package]]
name = "iceberg-catalog-s3tables"
version = "0.4.0"
source = "git+https://github.com/databendlabs/iceberg-rust?rev=d5cca1c15f240f3cb04e57569bce648933b1c79b#d5cca1c15f240f3cb04e57569bce648933b1c79b"
dependencies = [
 "anyhow",
 "async-trait",
 "aws-config",
 "aws-sdk-s3tables",
 "iceberg",
 "serde_json",
 "typed-builder 0.20.1",
 "uuid",
]

[[package]]
name = "icu_collections"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "200072f5d0e3614556f94a9930d5dc3e0662a652823904c3a75dc3b0af7fee47"
dependencies = [
 "displaydoc",
 "potential_utf",
 "yoke",
 "zerofrom",
 "zerovec",
]

[[package]]
name = "icu_locale_core"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0cde2700ccaed3872079a65fb1a78f6c0a36c91570f28755dda67bc8f7d9f00a"
dependencies = [
 "displaydoc",
 "litemap",
 "tinystr",
 "writeable",
 "zerovec",
]

[[package]]
name = "icu_normalizer"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "436880e8e18df4d7bbc06d58432329d6458cc84531f7ac5f024e93deadb37979"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_normalizer_data",
 "icu_properties",
 "icu_provider",
 "smallvec",
 "zerovec",
]

[[package]]
name = "icu_normalizer_data"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00210d6893afc98edb752b664b8890f0ef174c8adbb8d0be9710fa66fbbf72d3"

[[package]]
name = "icu_properties"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2549ca8c7241c82f59c80ba2a6f415d931c5b58d24fb8412caa1a1f02c49139a"
dependencies = [
 "displaydoc",
 "icu_collections",
 "icu_locale_core",
 "icu_properties_data",
 "icu_provider",
 "potential_utf",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "icu_properties_data"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8197e866e47b68f8f7d95249e172903bec06004b18b2937f1095d40a0c57de04"

[[package]]
name = "icu_provider"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03c80da27b5f4187909049ee2d72f276f0d9f99a42c306bd0131ecfe04d8e5af"
dependencies = [
 "displaydoc",
 "icu_locale_core",
 "stable_deref_trait",
 "tinystr",
 "writeable",
 "yoke",
 "zerofrom",
 "zerotrie",
 "zerovec",
]

[[package]]
name = "id-arena"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25a2bc672d1148e28034f176e01fffebb08b35768468cc954630da77a1449005"

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "686f825264d630750a544639377bae737628043f20d38bbc029e8f29ea968a7e"
dependencies = [
 "idna_adapter",
 "smallvec",
 "utf8_iter",
]

[[package]]
name = "idna_adapter"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3acae9609540aa318d1bc588455225fb2085b9ed0c4f6bd0d9d5bcd86f1a0344"
dependencies = [
 "icu_normalizer",
 "icu_properties",
]

[[package]]
name = "im"
version = "15.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0acd33ff0285af998aaf9b57342af478078f53492322fafc47450e09397e0e9"
dependencies = [
 "bitmaps",
 "rand_core 0.6.4",
 "rand_xoshiro",
 "sized-chunks",
 "typenum",
 "version_check",
]

[[package]]
name = "imara-diff"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17d34b7d42178945f775e84bc4c36dde7c1c6cdfea656d3354d009056f2bb3d2"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "include-flate"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df49c16750695486c1f34de05da5b7438096156466e7f76c38fcdf285cf0113e"
dependencies = [
 "include-flate-codegen",
 "lazy_static",
 "libflate",
]

[[package]]
name = "include-flate-codegen"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c5b246c6261be723b85c61ecf87804e8ea4a35cb68be0ff282ed84b95ffe7d7"
dependencies = [
 "libflate",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "indent"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9f1a0777d972970f204fdf8ef319f1f4f8459131636d7e3c96c5d59570d0fa6"

[[package]]
name = "indenter"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce23b50ad8242c51a442f3ff322d56b02f08852c77e4c0b4d3fd684abc89c683"

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cea70ddb795996207ad57735b50c5982d8844f38ba9ee5f1aedcfb708a2aa11e"
dependencies = [
 "equivalent",
 "hashbrown 0.15.3",
 "serde",
]

[[package]]
name = "indicatif"
version = "0.17.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "183b3088984b400f4cfac3620d5e076c84da5364016b4f49473de574b2586235"
dependencies = [
 "console",
 "number_prefix",
 "portable-atomic",
 "unicode-width 0.2.0",
 "web-time",
]

[[package]]
name = "indoc"
version = "2.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c7245a08504955605670dbf141fceab975f15ca21570696aebe9d2e71576bd"

[[package]]
name = "inferno"
version = "0.11.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "232929e1d75fe899576a3d5c7416ad0d88dbfbb3c3d6aa00873a7408a50ddb88"
dependencies = [
 "ahash 0.8.12",
 "indexmap 2.9.0",
 "is-terminal",
 "itoa",
 "log",
 "num-format",
 "once_cell",
 "quick-xml 0.26.0",
 "rgb",
 "str_stack",
]

[[package]]
name = "inotify"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8069d3ec154eb856955c1c0fbffefbf5f3c40a104ec912d4797314c1801abff"
dependencies = [
 "bitflags 1.3.2",
 "inotify-sys",
 "libc",
]

[[package]]
name = "inotify-sys"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e05c02b5e89bff3b946cedeca278abc628fe811e604f027c45a8aa3cf793d0eb"
dependencies = [
 "libc",
]

[[package]]
name = "inout"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "879f10e63c20629ecabbb64a8010319738c66a5cd0c29b02d63d272b03751d01"
dependencies = [
 "block-padding",
 "generic-array",
]

[[package]]
name = "instant"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0242819d153cba4b4b05a5a8f2a7e9bbf97b6055b2a002b395c96b5ff3c0222"
dependencies = [
 "cfg-if",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "integer-encoding"
version = "3.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bb03732005da905c88227371639bf1ad885cc712789c011c31c5fb3ab3ccf02"

[[package]]
name = "integer-encoding"
version = "4.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d762194228a2f1c11063e46e32e5acb96e66e906382b9eb5441f2e0504bbd5a"
dependencies = [
 "async-trait",
 "tokio",
]

[[package]]
name = "inventory"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab08d7cd2c5897f2c949e5383ea7c7db03fb19130ffcfbf7eda795137ae3cb83"
dependencies = [
 "rustversion",
]

[[package]]
name = "io-close"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9cadcf447f06744f8ce713d2d6239bb5bde2c357a452397a9ed90c625da390bc"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "io-extras"
version = "0.18.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2285ddfe3054097ef4b2fe909ef8c3bcd1ea52a8f0d274416caebeef39f04a65"
dependencies = [
 "io-lifetimes",
 "windows-sys 0.59.0",
]

[[package]]
name = "io-lifetimes"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06432fb54d3be7964ecd3649233cddf80db2832f47fec34c01f65b3d9d774983"

[[package]]
name = "ipconfig"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b58db92f96b720de98181bbbe63c831e87005ab460c1bf306eb2622b4707997f"
dependencies = [
 "socket2",
 "widestring",
 "windows-sys 0.48.0",
 "winreg",
]

[[package]]
name = "ipnet"
version = "2.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "469fb0b9cefa57e3ef31275ee7cacb78f2fdca44e4765491884a2b119d4eb130"

[[package]]
name = "is-terminal"
version = "0.4.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e04d7f318608d35d4b61ddd75cbdaee86b023ebe2bd5a66ee0915f0bf93095a9"
dependencies = [
 "hermit-abi 0.5.1",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "is_terminal_polyfill"
version = "1.70.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7943c866cc5cd64cbc25b2e01621d07fa8eb2a1a23160ee81ce38704e97b8ecf"

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413ee7dfc52ee1a4949ceeb7dbc8a33f2d6c088194d9f922fb8318faf1f01186"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b192c782037fadd9cfa75548310488aabdbf3d2da73885b31bd0abd03351285"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a5f13b858c8d314ee3e8f639011f7ccefe71f97f96e50151fb991f267928e2c"

[[package]]
name = "ittapi"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b996fe614c41395cdaedf3cf408a9534851090959d90d54a535f675550b64b1"
dependencies = [
 "anyhow",
 "ittapi-sys",
 "log",
]

[[package]]
name = "ittapi-sys"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52f5385394064fa2c886205dba02598013ce83d3e92d33dbdc0c52fe0e7bf4fc"
dependencies = [
 "cc",
]

[[package]]
name = "jaq-core"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6fda09ee08c84c81293fdf811d9ebaa87b327557b5391f290c926d728c2ddd4"
dependencies = [
 "aho-corasick",
 "base64 0.22.1",
 "chrono",
 "hifijson",
 "jaq-interpret",
 "libm",
 "log",
 "regex",
 "urlencoding",
]

[[package]]
name = "jaq-interpret"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fe95ec3c24af3fd9f3dd1091593f5e49b003a66c496a8aa39d764d0a06ae17b"
dependencies = [
 "ahash 0.8.12",
 "dyn-clone",
 "hifijson",
 "indexmap 2.9.0",
 "jaq-syn",
 "once_cell",
 "serde_json",
]

[[package]]
name = "jaq-parse"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0346d7d3146cdda8acd929581f3d6626a332356c74d5c95aeaffaac2eb6dee82"
dependencies = [
 "chumsky",
 "jaq-syn",
]

[[package]]
name = "jaq-std"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfbaa55578fd3b70433b594a370741e0c364e4afff92cc0099623fce87311bc1"
dependencies = [
 "jaq-syn",
]

[[package]]
name = "jaq-syn"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ba44fe4428c71304604261ecbae047ee9cfb60c4f1a6bd222ebbb31726d3948"
dependencies = [
 "serde",
]

[[package]]
name = "java-locator"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09c46c1fe465c59b1474e665e85e1256c3893dd00927b8d55f63b09044c1e64f"
dependencies = [
 "glob",
]

[[package]]
name = "jieba-macros"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c676b32a471d3cfae8dac2ad2f8334cd52e53377733cca8c1fb0a5062fec192"
dependencies = [
 "phf_codegen",
]

[[package]]
name = "jieba-rs"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d1bcad6332969e4d48ee568d430e14ee6dea70740c2549d005d87677ebefb0c"
dependencies = [
 "cedarwood",
 "fxhash",
 "include-flate",
 "jieba-macros",
 "lazy_static",
 "phf",
 "regex",
]

[[package]]
name = "jiff"
version = "0.1.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c04ef77ae73f3cf50510712722f0c4e8b46f5aaa1bf5ffad2ae213e6495e78e5"
dependencies = [
 "jiff-tzdb-platform",
 "log",
 "portable-atomic",
 "portable-atomic-util",
 "serde",
 "windows-sys 0.59.0",
]

[[package]]
name = "jiff"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f02000660d30638906021176af16b17498bd0d12813dbfe7b276d8bc7f3c0806"
dependencies = [
 "jiff-static",
 "jiff-tzdb",
 "jiff-tzdb-platform",
 "log",
 "portable-atomic",
 "portable-atomic-util",
 "serde",
 "windows-sys 0.59.0",
]

[[package]]
name = "jiff-static"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3c30758ddd7188629c6713fc45d1188af4f44c90582311d0c8d8c9907f60c48"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "jiff-tzdb"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1283705eb0a21404d2bfd6eef2a7593d240bc42a0bdb39db0ad6fa2ec026524"

[[package]]
name = "jiff-tzdb-platform"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "875a5a69ac2bab1a891711cf5eccbec1ce0341ea805560dcd90b7a2e925132e8"
dependencies = [
 "jiff-tzdb",
]

[[package]]
name = "jobserver"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f262f097c174adebe41eb73d66ae9c06b2844fb0da69969647bbddd9b0538a"
dependencies = [
 "getrandom 0.3.3",
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cfaf33c695fc6e08064efbc1f72ec937429614f25eef83af942d0e227c3a28f"
dependencies = [
 "once_cell",
 "wasm-bindgen",
]

[[package]]
name = "jsonb"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "153dfebb6ad4484c84a5ae765a18976bf476b8a3f5165ce987aedd59d1f1e6c8"
dependencies = [
 "byteorder",
 "ethnum",
 "fast-float2",
 "itoa",
 "jiff 0.2.13",
 "nom 8.0.0",
 "num-traits",
 "ordered-float 5.0.0",
 "rand 0.9.1",
 "ryu",
 "serde",
 "serde_json",
]

[[package]]
name = "jsonwebtoken"
version = "9.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a87cc7a48537badeae96744432de36f4be2b4a34a05a5ef32e9dd8a1c169dde"
dependencies = [
 "base64 0.22.1",
 "js-sys",
 "pem",
 "ring",
 "serde",
 "serde_json",
 "simple_asn1",
]

[[package]]
name = "jwalk"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2735847566356cd2179a2a38264839308f7079fa96e6bd5a42d740460e003c56"
dependencies = [
 "crossbeam",
 "rayon",
]

[[package]]
name = "jwt-simple"
version = "0.12.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "731011e9647a71ff4f8474176ff6ce6e0d2de87a0173f15613af3a84c3e3401a"
dependencies = [
 "anyhow",
 "binstring",
 "blake2b_simd",
 "coarsetime",
 "ct-codecs",
 "ed25519-compact",
 "hmac-sha1-compact",
 "hmac-sha256",
 "hmac-sha512",
 "k256",
 "p256",
 "p384",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "superboring",
 "thiserror 2.0.12",
 "zeroize",
]

[[package]]
name = "k256"
version = "0.13.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6e3919bbaa2945715f0bb6d3934a173d1e9a59ac23767fbaaef277265a7411b"
dependencies = [
 "cfg-if",
 "ecdsa",
 "elliptic-curve",
 "once_cell",
 "sha2",
 "signature",
]

[[package]]
name = "keyed_priority_queue"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ee7893dab2e44ae5f9d0173f26ff4aa327c10b01b06a72b52dd9405b628640d"
dependencies = [
 "indexmap 2.9.0",
]

[[package]]
name = "konst"
version = "0.3.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4381b9b00c55f251f2ebe9473aef7c117e96828def1a7cb3bd3f0f903c6894e9"
dependencies = [
 "const_panic",
 "konst_kernel",
 "typewit",
]

[[package]]
name = "konst_kernel"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4b1eb7788f3824c629b1116a7a9060d6e898c358ebff59070093d51103dcc3c"
dependencies = [
 "typewit",
]

[[package]]
name = "kqueue"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac30106d7dce88daf4a3fcb4879ea939476d5074a9b7ddd0fb97fa4bed5596a"
dependencies = [
 "kqueue-sys",
 "libc",
]

[[package]]
name = "kqueue-sys"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed9625ffda8729b85e45cf04090035ac368927b8cebc34898e7c120f52e4838b"
dependencies = [
 "bitflags 1.3.2",
 "libc",
]

[[package]]
name = "kstring"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "558bf9508a558512042d3095138b1f7b8fe90c5467d94f9f1da28b3731c5dbd1"
dependencies = [
 "static_assertions",
]

[[package]]
name = "lazy_static"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbd2bcb4c963f2ddae06a2efc7e9f3591312473c50c6685e1f298068316e66fe"
dependencies = [
 "spin 0.9.8",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "leb128"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "884e2677b40cc8c339eaefcb701c32ef1fd2493d71118dc0ca4b6a736c93bd67"

[[package]]
name = "leb128fmt"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09edd9e8b54e49e587e4f6295a7d29c3ea94d469cb40ab8ca70b288248a81db2"

[[package]]
name = "lenient_semver"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de8de3f4f3754c280ce1c8c42ed8dd26a9c8385c2e5ad4ec5a77e774cea9c1ec"
dependencies = [
 "lenient_semver_parser",
 "semver",
]

[[package]]
name = "lenient_semver_parser"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f650c1d024ddc26b4bb79c3076b30030f2cf2b18292af698c81f7337a64d7d6"
dependencies = [
 "lenient_semver_version_builder",
 "semver",
]

[[package]]
name = "lenient_semver_version_builder"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9049f8ff49f75b946f95557148e70230499c8a642bf2d6528246afc7d0282d17"
dependencies = [
 "semver",
]

[[package]]
name = "levenshtein_automata"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2cdeb66e45e9f36bfad5bbdb4d2384e70936afbee843c6f6543f0c551ebb25"

[[package]]
name = "lexical-core"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b765c31809609075565a70b4b71402281283aeda7ecaf4818ac14a7b2ade8958"
dependencies = [
 "lexical-parse-float",
 "lexical-parse-integer",
 "lexical-util",
 "lexical-write-float",
 "lexical-write-integer",
]

[[package]]
name = "lexical-parse-float"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de6f9cb01fb0b08060209a057c048fcbab8717b4c1ecd2eac66ebfe39a65b0f2"
dependencies = [
 "lexical-parse-integer",
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-parse-integer"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72207aae22fc0a121ba7b6d479e42cbfea549af1479c3f3a4f12c70dd66df12e"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "lexical-util"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a82e24bf537fd24c177ffbbdc6ebcc8d54732c35b50a3f28cc3f4e4c949a0b3"
dependencies = [
 "static_assertions",
]

[[package]]
name = "lexical-write-float"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5afc668a27f460fb45a81a757b6bf2f43c2d7e30cb5a2dcd3abf294c78d62bd"
dependencies = [
 "lexical-util",
 "lexical-write-integer",
 "static_assertions",
]

[[package]]
name = "lexical-write-integer"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "629ddff1a914a836fb245616a7888b62903aae58fa771e1d83943035efa0f978"
dependencies = [
 "lexical-util",
 "static_assertions",
]

[[package]]
name = "libc"
version = "0.2.172"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d750af042f7ef4f724306de029d18836c26c1765a54a6a3f094cbd23a7267ffa"

[[package]]
name = "libflate"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45d9dfdc14ea4ef0900c1cddbc8dcd553fbaacd8a4a282cf4018ae9dd04fb21e"
dependencies = [
 "adler32",
 "core2",
 "crc32fast",
 "dary_heap",
 "libflate_lz77",
]

[[package]]
name = "libflate_lz77"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6e0d73b369f386f1c44abd9c570d5318f55ccde816ff4b562fa452e5182863d"
dependencies = [
 "core2",
 "hashbrown 0.14.5",
 "rle-decode-fast",
]

[[package]]
name = "libloading"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a793df0d7afeac54f95b471d3af7f0d4fb975699f972341a4b76988d49cdf0c"
dependencies = [
 "cfg-if",
 "windows-targets 0.53.0",
]

[[package]]
name = "libm"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9fbbcab51052fe104eb5e5d351cf728d30a5be1fe14d9be8a3b097481fb97de"

[[package]]
name = "libredox"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c0ff37bd590ca25063e35af745c343cb7a0271906fb7b37e4813e8f79f00268d"
dependencies = [
 "bitflags 2.9.0",
 "libc",
 "redox_syscall 0.5.12",
]

[[package]]
name = "libsqlite3-sys"
version = "0.30.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e99fb7a497b1e3339bc746195567ed8d3e24945ecd636e3619d20b9de9e9149"
dependencies = [
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "libtest-mimic"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5297962ef19edda4ce33aaa484386e0a5b3d7f2f4e037cbeee00503ef6b29d33"
dependencies = [
 "anstream",
 "anstyle",
 "clap",
 "escape8259",
]

[[package]]
name = "libz-rs-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6489ca9bd760fe9642d7644e827b0c9add07df89857b0416ee15c1cc1a3b8c5a"
dependencies = [
 "zlib-rs",
]

[[package]]
name = "libz-sys"
version = "1.1.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b70e7a7df205e92a1a4cd9aaae7898dac0aa555503cc0a649494d0d60e7651d"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "limits-rs"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19d3f1678107a67d955d3118a3f1d93db692c514849b53bd1746f5195d28a1fc"
dependencies = [
 "thiserror 1.0.69",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linkedbytes"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "487640b2e3f554987c1345fc71c3eda34d6d750fba63a39147205ccc93f920cd"
dependencies = [
 "bytes",
 "faststr",
 "tokio",
]

[[package]]
name = "linux-raw-sys"
version = "0.4.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d26c52dbd32dccf2d10cac7725f8eae5296885fb5703b261f7d0a0739ec807ab"

[[package]]
name = "linux-raw-sys"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd945864f07fe9f5371a27ad7b52a172b4b499999f1d97574c9fa68373937e12"

[[package]]
name = "litemap"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "241eaef5fd12c88705a01fc1066c48c4b36e0dd4377dcdc7ec3942cea7a69956"

[[package]]
name = "litrs"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ce301924b7887e9d637144fdade93f9dfff9b60981d4ac161db09720d39aa5"

[[package]]
name = "lock_api"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07af8b9cdd281b7915f413fa73f29ebd5d55d0d3f0155584dade1ff18cea1b17"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13dc2df351e3202783a1fe0d44375f7295ffb4049267b0f3018346dc122a1d94"
dependencies = [
 "serde",
 "value-bag",
]

[[package]]
name = "logcall"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e8309d7cbf9e9f27139956138cb375f14621cdb2f4cdd91468467ec04b6784"
dependencies = [
 "proc-macro-error 1.0.4",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "logforth"
version = "0.14.0"
source = "git+https://github.com/datafuse-extras/logforth?branch=global-max-files-v0.14#cedfff08d59a683ac2bf08ff54724e0b43bd218b"
dependencies = [
 "anyhow",
 "colored",
 "crossbeam-channel",
 "env_filter",
 "fastrace",
 "jiff 0.1.29",
 "log",
 "opentelemetry",
 "opentelemetry-otlp",
 "opentelemetry_sdk",
 "parking_lot 0.12.3",
 "paste",
 "serde",
 "serde_json",
]

[[package]]
name = "logos"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf8b031682c67a8e3d5446840f9573eb7fe26efe7ec8d195c9ac4c0647c502f1"
dependencies = [
 "logos-derive",
]

[[package]]
name = "logos-derive"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d849148dbaf9661a6151d1ca82b13bb4c4c128146a88d05253b38d4e2f496c"
dependencies = [
 "beef",
 "fnv",
 "proc-macro2",
 "quote",
 "regex-syntax 0.6.29",
 "syn 1.0.109",
]

[[package]]
name = "loom"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff50ecb28bb86013e935fb6683ab1f6d3a20016f123c76fd4c27470076ac30f5"
dependencies = [
 "cfg-if",
 "generator 0.7.5",
 "scoped-tls",
 "serde",
 "serde_json",
 "tracing",
 "tracing-subscriber",
]

[[package]]
name = "loom"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "419e0dc8046cb947daa77eb95ae174acfbddb7673b4151f56d1eed8e93fbfaca"
dependencies = [
 "cfg-if",
 "generator 0.8.5",
 "scoped-tls",
 "tracing",
 "tracing-subscriber",
]

[[package]]
name = "lru"
version = "0.12.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "234cf4f4a04dc1f57e24b96cc0cd600cf2af460d4161ac5ecdd0af8e1f3b2a38"
dependencies = [
 "hashbrown 0.15.3",
]

[[package]]
name = "lru-cache-map"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "227ef6c03002a1a4e03948ed0c7ba71375962a7e6be06ea6880c5ad96b847f85"
dependencies = [
 "hashbrown 0.14.5",
 "hashlink 0.8.4",
]

[[package]]
name = "lru-slab"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "112b39cec0b298b6c1999fee3e31427f74f676e4cb9879ed1a121b43661a4154"

[[package]]
name = "lz4"
version = "1.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a20b523e860d03443e98350ceaac5e71c6ba89aea7d960769ec3ce37f4de5af4"
dependencies = [
 "lz4-sys",
]

[[package]]
name = "lz4-sys"
version = "1.11.1+lz4-1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bd8c0d6c6ed0cd30b3652886bb8711dc4bb01d637a68105a3d5158039b418e6"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "lz4_flex"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75761162ae2b0e580d7e7c390558127e5f01b4194debd6221fd8c207fc80e3f5"
dependencies = [
 "twox-hash 1.6.3",
]

[[package]]
name = "lzma-rs"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "297e814c836ae64db86b36cf2a557ba54368d03f6afcd7d947c266692f71115e"
dependencies = [
 "byteorder",
 "crc",
]

[[package]]
name = "lzma-sys"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fda04ab3764e6cde78b9974eec4f779acaba7c4e84b36eca3cf77c581b85d27"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "lzokay-native"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "792ba667add2798c6c3e988e630f4eb921b5cbc735044825b7111ef1582c8730"
dependencies = [
 "byteorder",
 "thiserror 1.0.69",
]

[[package]]
name = "mach2"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b955cdeb2a02b9117f121ce63aa52d08ade45de53e48fe6a38b39c10f6f709"
dependencies = [
 "libc",
]

[[package]]
name = "map-api"
version = "0.2.3"
source = "git+https://github.com/databendlabs/map-api?tag=v0.2.3#e4de1786b63d2c401413413ac8069f305c849c06"
dependencies = [
 "async-trait",
 "deepsize",
 "futures",
 "futures-util",
 "log",
 "serde",
 "stream-more",
 "thiserror 1.0.69",
]

[[package]]
name = "maplit"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e2e65a1a2e43cfcb47a895c4c8b10d1f4a61097f9f254f183aee60cad9c651d"

[[package]]
name = "match-template"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c334ac67725febd94c067736ac46ef1c7cacf1c743ca14b9f917c2df2c20acd8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "match_cfg"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffbee8634e0d45d258acb448e7eaab3fce7a0a467395d4d9f228e3c1f01fb2e4"

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matchit"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7465ac9959cc2b1404e8e2367b43684a6d13790fe23056cc8c6c5a6b7bcb94"

[[package]]
name = "matrixmultiply"
version = "0.3.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06de3016e9fae57a36fd14dba131fccf49f74b40b7fbdb472f96e361ec71a08"
dependencies = [
 "autocfg",
 "rawpointer",
]

[[package]]
name = "maybe-owned"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4facc753ae494aeb6e3c22f839b158aebd4f9270f55cd3c79906c45476c47ab4"

[[package]]
name = "md-5"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d89e7ee0cfbedfc4da3340218492196241d89eefb6dab27de5df917a6d2e78cf"
dependencies = [
 "cfg-if",
 "digest",
]

[[package]]
name = "measure_time"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbefd235b0aadd181626f281e1d684e116972988c14c264e42069d5e8a5775cc"
dependencies = [
 "instant",
 "log",
]

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"

[[package]]
name = "memfd"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2cffa4ad52c6f791f4f8b15f0c05f9824b2ced1160e88cc393d64fff9a8ac64"
dependencies = [
 "rustix 0.38.44",
]

[[package]]
name = "memmap2"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd3f7eed9d3848f8b98834af67102b720745c4ec028fcd0aa0239277e7de374f"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "488016bfae457b036d996092f6cb448677611ce4449e970ceaf42695203f218a"
dependencies = [
 "autocfg",
]

[[package]]
name = "metainfo"
version = "0.7.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cdc67a1d6ef0340a7f5152b9fd34ad7477b4d518920f7557267f1fc6e5a62641"
dependencies = [
 "ahash 0.8.12",
 "faststr",
 "paste",
 "rustc-hash 2.1.1",
 "tokio",
]

[[package]]
name = "micromarshal"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "863693c933db83464c52adf30809f583aae924f30fa114f303aa5360b4a399ec"
dependencies = [
 "ethnum",
 "ordered-float 4.6.0",
]

[[package]]
name = "mime"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6877bb514081ee2a7ff5ef9de3281f14a4dd4bceac4c09388074a6b5df8a139a"

[[package]]
name = "minijinja"
version = "0.31.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673d1ece89f7e166f43270800d78f9b1a9d21fda92dbcfa3b98b21213cdccbbc"
dependencies = [
 "serde",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8a240ddb74feaf34a79a7add65a741f3167852fba007066dcac1ca548d89c08"
dependencies = [
 "adler",
]

[[package]]
name = "miniz_oxide"
version = "0.8.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be647b768db090acb35d5ec5db2b0e1f1de11133ca123b9eacf5137868f892a"
dependencies = [
 "adler2",
]

[[package]]
name = "mio"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4a650543ca06a924e8b371db273b2756685faae30f8487da1b56505a8f78b0c"
dependencies = [
 "libc",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.48.0",
]

[[package]]
name = "mio"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2886843bf800fba2e3377cff24abf6379b4c4d5c6681eaf9ea5b0d15090450bd"
dependencies = [
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "mockall"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c84490118f2ee2d74570d114f3d0493cbf02790df303d2707606c3e14e07c96"
dependencies = [
 "cfg-if",
 "downcast",
 "fragile",
 "lazy_static",
 "mockall_derive",
 "predicates",
 "predicates-tree",
]

[[package]]
name = "mockall_derive"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22ce75669015c4f47b289fd4d4f56e894e4c96003ffdf3ac51313126f94c6cbb"
dependencies = [
 "cfg-if",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "moka"
version = "0.12.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9321642ca94a4282428e6ea4af8cc2ca4eac48ac7a6a4ea8f33f76d0ce70926"
dependencies = [
 "async-lock",
 "crossbeam-channel",
 "crossbeam-epoch",
 "crossbeam-utils",
 "event-listener 5.4.0",
 "futures-util",
 "loom 0.7.2",
 "parking_lot 0.12.3",
 "portable-atomic",
 "rustc_version",
 "smallvec",
 "tagptr",
 "thiserror 1.0.69",
 "uuid",
]

[[package]]
name = "motore"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "983b283a2be61606ca0c16b1a8ab2110b2eb0d7284293658d510e91bd705508a"
dependencies = [
 "futures",
 "motore-macros",
 "pin-project",
 "tokio",
]

[[package]]
name = "motore-macros"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b40e46c845ac234bcba19db7ab252bc2778cbadd516a466d2f12b1580852d136"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "multer"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83e87776546dc87511aa5ee218730c92b666d7264ab6ed41f9d215af9cd5224b"
dependencies = [
 "bytes",
 "encoding_rs",
 "futures-util",
 "http 1.3.1",
 "httparse",
 "memchr",
 "mime",
 "spin 0.9.8",
 "tokio",
 "version_check",
]

[[package]]
name = "multimap"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d87ecb2933e8aeadb3e3a02b828fed80a7528047e68b4f424523a0981a3a084"

[[package]]
name = "munge"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e22e7961c873e8b305b176d2a4e1d41ce7ba31bc1c52d2a107a89568ec74c55"
dependencies = [
 "munge_macro",
]

[[package]]
name = "munge_macro"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ac7d860b767c6398e88fe93db73ce53eb496057aa6895ffa4d60cb02e1d1c6b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "mur3"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97af489e1e21b68de4c390ecca6703318bc1aa16e9733bcb62c089b73c6fbb1b"

[[package]]
name = "murmur3"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9252111cf132ba0929b6f8e030cac2a24b507f3a4d6db6fb2896f27b354c714b"

[[package]]
name = "murmurhash32"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2195bf6aa996a481483b29d62a7663eed3fe39600c460e323f8ff41e90bdd89b"

[[package]]
name = "mysql-common-derive"
version = "0.31.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "63c3512cf11487168e0e9db7157801bf5273be13055a9cc95356dc9e0035e49c"
dependencies = [
 "darling",
 "heck 0.5.0",
 "num-bigint",
 "proc-macro-crate 3.3.0",
 "proc-macro-error2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "termcolor",
 "thiserror 1.0.69",
]

[[package]]
name = "mysql_async"
version = "0.34.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0b66e411c31265e879d9814d03721f2daa7ad07337b6308cb4bb0cde7e6fd47"
dependencies = [
 "bytes",
 "crossbeam",
 "flate2",
 "futures-core",
 "futures-sink",
 "futures-util",
 "keyed_priority_queue",
 "lru",
 "mysql_common",
 "native-tls",
 "pem",
 "percent-encoding",
 "pin-project",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "socket2",
 "thiserror 1.0.69",
 "tokio",
 "tokio-native-tls",
 "tokio-util",
 "twox-hash 1.6.3",
 "url",
]

[[package]]
name = "mysql_common"
version = "0.32.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "478b0ff3f7d67b79da2b96f56f334431aef65e15ba4b29dd74a4236e29582bdc"
dependencies = [
 "base64 0.21.7",
 "bigdecimal",
 "bindgen 0.71.1",
 "bitflags 2.9.0",
 "bitvec",
 "btoi",
 "byteorder",
 "bytes",
 "cc",
 "chrono",
 "cmake",
 "crc32fast",
 "flate2",
 "frunk",
 "lazy_static",
 "mysql-common-derive",
 "num-bigint",
 "num-traits",
 "rand 0.8.5",
 "regex",
 "rust_decimal",
 "saturating",
 "serde",
 "serde_json",
 "sha1",
 "sha2",
 "smallvec",
 "subprocess",
 "thiserror 1.0.69",
 "time",
 "uuid",
 "zstd 0.13.3",
]

[[package]]
name = "naive-cityhash"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fce7b49e1e6d8aa67232ef1c4c936c0af58756eb2db6f65c40bacb39035e7f42"

[[package]]
name = "native-tls"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87de3442987e9dbec73158d5c715e7ad9072fda936bb03d19d7fa10e00520f0e"
dependencies = [
 "libc",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework 2.11.1",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "ndarray"
version = "0.15.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb12d4e967ec485a5f71c6311fe28158e9d6f4bc4a447b474184d0f91a8fa32"
dependencies = [
 "matrixmultiply",
 "num-complex",
 "num-integer",
 "num-traits",
 "rawpointer",
]

[[package]]
name = "nibble_vec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a5d83df9f36fe23f0c3648c6bbb8b0298bb5f1939c8f2704431371f4b84d43"
dependencies = [
 "smallvec",
]

[[package]]
name = "nix"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "598beaf3cc6fdd9a5dfb1630c2800c7acd31df7aaf0f565796fba2b53ca1af1b"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if",
 "libc",
]

[[package]]
name = "nix"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab2156c4fce2f8df6c499cc1c763e4394b7482525bf2a9701c9d79d215f519e4"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.1.1",
 "libc",
]

[[package]]
name = "nix"
version = "0.29.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71e2746dc3a24dd78b3cfcb7be93368c6de9963d30f43a6a73998a9cf4b17b46"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.2.1",
 "libc",
 "memoffset",
]

[[package]]
name = "nix"
version = "0.30.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74523f3a35e05aba87a1d978330aef40f67b0304ac79c1c00b294c9830543db6"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "cfg_aliases 0.2.1",
 "libc",
]

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nom"
version = "8.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df9761775871bdef83bee530e60050f7e54b1105350d6884eb0fb4f46c2f9405"
dependencies = [
 "memchr",
]

[[package]]
name = "nom-rule"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c72951bd83c76b88d820f03b38b124f981dc2520070d62170da7012d1ce964ab"
dependencies = [
 "nom 7.1.3",
 "pratt",
 "proc-macro-error 1.0.4",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "normalize-line-endings"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61807f77802ff30975e01f4f071c8ba10c022052f98b3294119f3e615d13e5be"

[[package]]
name = "notify"
version = "6.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6205bd8bb1e454ad2e27422015fb5e4f2bcc7e08fa8f27058670d208324a4d2d"
dependencies = [
 "bitflags 2.9.0",
 "crossbeam-channel",
 "filetime",
 "fsevent-sys",
 "inotify",
 "kqueue",
 "libc",
 "log",
 "mio 0.8.11",
 "walkdir",
 "windows-sys 0.48.0",
]

[[package]]
name = "ntapi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a3895c6391c39d7fe7ebc444a87eb2991b2a0bc718fdabd071eec617fc68e4"
dependencies = [
 "winapi",
]

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "num"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35bd024e8b2ff75562e5f34e7f4905839deb4b22955ef5e73d2fea1b9813cb23"
dependencies = [
 "num-bigint",
 "num-complex",
 "num-integer",
 "num-iter",
 "num-rational",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5e44f723f1133c9deac646763579fdb3ac745e418f2a7af9cd0c431da1f20b9"
dependencies = [
 "arbitrary",
 "num-integer",
 "num-traits",
 "serde",
]

[[package]]
name = "num-bigint-dig"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc84195820f291c7697304f3cbdadd1cb7199c0efc917ff5eafd71225c136151"
dependencies = [
 "byteorder",
 "lazy_static",
 "libm",
 "num-integer",
 "num-iter",
 "num-traits",
 "rand 0.8.5",
 "smallvec",
 "zeroize",
]

[[package]]
name = "num-complex"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f88a1307638156682bada9d7604135552957b7818057dcef22705b4d509495"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed3955f1a9c7c0c15e092f9c887db08b1fc683305fdf6eb6684f22555355e202"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1429034a0490724d0075ebb2bc9e875d6503c3cf69e235a8941aa757d83ef5bf"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f83d14da390562dca69fc84082e73e548e1ad308d24accdedd2720017cb37824"
dependencies = [
 "num-bigint",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "071dfc062690e90b734c0b2273ce72ad0ffa95f0c74596bc250dcfd960262841"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
]

[[package]]
name = "num_enum"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e613fc340b2220f734a8595782c551f1250e969d87d3be1ae0579e8d4065179"
dependencies = [
 "num_enum_derive",
]

[[package]]
name = "num_enum_derive"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af1844ef2428cc3e1cb900be36181049ef3d3193c63e43026cfe202983b27a56"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "num_threads"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c7398b9c8b70908f6371f47ed36737907c87c52af34c268fed0bf0ceb92ead9"
dependencies = [
 "libc",
]

[[package]]
name = "number_prefix"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b246a0e5f20af87141b25c173cd1b609bd7779a4617d6ec582abaf90870f3"

[[package]]
name = "objc2-core-foundation"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c10c2894a6fed806ade6027bcd50662746363a9589d3ec9d9bef30a4e4bc166"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "crc32fast",
 "flate2",
 "hashbrown 0.15.3",
 "indexmap 2.9.0",
 "memchr",
 "ruzstd",
]

[[package]]
name = "object_store"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d94ac16b433c0ccf75326388c893d2835ab7457ea35ab8ba5d745c053ef5fa16"
dependencies = [
 "async-trait",
 "base64 0.22.1",
 "bytes",
 "chrono",
 "form_urlencoded",
 "futures",
 "http 1.3.1",
 "http-body-util",
 "httparse",
 "humantime",
 "hyper 1.6.0",
 "itertools 0.14.0",
 "md-5",
 "parking_lot 0.12.3",
 "percent-encoding",
 "quick-xml 0.37.5",
 "rand 0.9.1",
 "reqwest",
 "ring",
 "rustls-pemfile 2.2.0",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "url",
 "walkdir",
 "wasm-bindgen-futures",
 "web-time",
]

[[package]]
name = "object_store_opendal"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "953cacb624212ffad8f34818584b33715d8ba5b1c9bc82c38b2e47a646e7e362"
dependencies = [
 "async-trait",
 "bytes",
 "futures",
 "object_store",
 "opendal",
 "pin-project",
 "tokio",
]

[[package]]
name = "once_cell"
version = "1.21.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42f5e15c9953c5e4ccceeb2e7382a716482c34515315f7b03532b8b4e8393d2d"
dependencies = [
 "critical-section",
 "portable-atomic",
]

[[package]]
name = "oneshot"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ce411919553d3f9fa53a0880544cda985a112117a0444d5ff1e870a893d6ea"

[[package]]
name = "opaque-debug"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08d65885ee38876c4f86fa503fb49d7b507c2b62552df7c70b2fce627e06381"

[[package]]
name = "openai_api_rust"
version = "0.1.4"
source = "git+https://github.com/datafuse-extras/openai-api?rev=819a0ed#819a0ed1384e4fe948b2bec88856e37062cb0f38"
dependencies = [
 "log",
 "serde",
 "serde_json",
 "ureq",
]

[[package]]
name = "opendal"
version = "0.53.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ff9e9656d1cb3c58582ea18e6d9e71076a7ab2614207821d1242d7da2daed5"
dependencies = [
 "anyhow",
 "async-backtrace",
 "async-trait",
 "backon",
 "base64 0.22.1",
 "bytes",
 "chrono",
 "crc32c",
 "fastrace",
 "futures",
 "getrandom 0.2.16",
 "hdrs",
 "http 1.3.1",
 "http-body 1.0.1",
 "log",
 "md-5",
 "moka",
 "percent-encoding",
 "prometheus-client 0.23.1",
 "prost",
 "quick-xml 0.37.5",
 "reqsign",
 "reqwest",
 "serde",
 "serde_json",
 "sha2",
 "tokio",
 "uuid",
]

[[package]]
name = "openraft"
version = "0.10.0"
source = "git+https://github.com/databendlabs/openraft?tag=v0.10.0-alpha.9#a529931e633e641cac09d078ed815a0d7c15a3c3"
dependencies = [
 "anyerror",
 "byte-unit",
 "chrono",
 "clap",
 "derive_more",
 "futures",
 "maplit",
 "openraft-macros",
 "rand 0.9.1",
 "serde",
 "thiserror 1.0.69",
 "tokio",
 "tracing",
 "tracing-futures",
 "validit",
]

[[package]]
name = "openraft-macros"
version = "0.10.0"
source = "git+https://github.com/databendlabs/openraft?tag=v0.10.0-alpha.9#a529931e633e641cac09d078ed815a0d7c15a3c3"
dependencies = [
 "chrono",
 "proc-macro2",
 "quote",
 "semver",
 "syn 2.0.101",
]

[[package]]
name = "opensrv-mysql"
version = "0.8.0"
source = "git+https://github.com/databendlabs/opensrv.git?rev=a1fb4da#a1fb4da215c8693c7e4f62be249a01b7fec52997"
dependencies = [
 "async-trait",
 "byteorder",
 "bytes",
 "chrono",
 "mysql_common",
 "nom 7.1.3",
 "pin-project-lite",
 "tokio",
 "tokio-rustls 0.26.2",
]

[[package]]
name = "openssl"
version = "0.10.72"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fedfea7d58a1f73118430a55da6a286e7b044961736ce96a16a17068ea25e5da"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "foreign-types",
 "libc",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a948666b637a0f465e8564c73e89d4dde00d72d4d473cc972f390fc3dcee7d9c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "openssl-probe"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d05e27ee213611ffe7d6348b942e8f942b37114c00cc03cec254295a4a17852e"

[[package]]
name = "openssl-src"
version = "300.5.0****.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8ce546f549326b0e6052b649198487d91320875da901e7bd11a06d1ee3f9c2f"
dependencies = [
 "cc",
]

[[package]]
name = "openssl-sys"
version = "0.9.108"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e145e1651e858e820e4860f7b9c5e169bc1d8ce1c86043be79fa7b7634821847"
dependencies = [
 "cc",
 "libc",
 "openssl-src",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "opentelemetry"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "570074cc999d1a58184080966e5bd3bf3a9a4af650c3b05047c2621e7405cd17"
dependencies = [
 "futures-core",
 "futures-sink",
 "js-sys",
 "once_cell",
 "pin-project-lite",
 "thiserror 1.0.69",
]

[[package]]
name = "opentelemetry-http"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6351496aeaa49d7c267fb480678d85d1cd30c5edb20b497c48c56f62a8c14b99"
dependencies = [
 "async-trait",
 "bytes",
 "http 1.3.1",
 "opentelemetry",
 "reqwest",
]

[[package]]
name = "opentelemetry-otlp"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29e1f9c8b032d4f635c730c0efcf731d5e2530ea13fa8bef7939ddc8420696bd"
dependencies = [
 "async-trait",
 "futures-core",
 "http 1.3.1",
 "opentelemetry",
 "opentelemetry-http",
 "opentelemetry-proto",
 "opentelemetry_sdk",
 "prost",
 "reqwest",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tonic",
]

[[package]]
name = "opentelemetry-proto"
version = "0.26.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9d3968ce3aefdcca5c27e3c4ea4391b37547726a70893aab52d3de95d5f8b34"
dependencies = [
 "hex",
 "opentelemetry",
 "opentelemetry_sdk",
 "prost",
 "serde",
 "tonic",
]

[[package]]
name = "opentelemetry_sdk"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2c627d9f4c9cdc1f21a29ee4bfbd6028fcb8bcf2a857b43f3abdf72c9c862f3"
dependencies = [
 "async-trait",
 "futures-channel",
 "futures-executor",
 "futures-util",
 "glob",
 "once_cell",
 "opentelemetry",
 "percent-encoding",
 "rand 0.8.5",
 "serde_json",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "orc-rust"
version = "0.6.0"
source = "git+https://github.com/datafuse-extras/orc-rust?rev=d82aa6d#d82aa6de06f49f8d3884d447041bf2d3c4daff4c"
dependencies = [
 "arrow",
 "async-trait",
 "bytemuck",
 "bytes",
 "chrono",
 "chrono-tz 0.10.3",
 "fallible-streaming-iterator",
 "flate2",
 "futures",
 "futures-util",
 "lz4_flex",
 "lzokay-native",
 "num",
 "prost",
 "snafu",
 "snap",
 "tokio",
 "zstd 0.13.3",
]

[[package]]
name = "ordered-float"
version = "2.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68f19d67e5a2795c94e73e0bb1cc1a7edeb2e28efd39e2e1c9b7a40c1108b11c"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-float"
version = "4.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bb71e1b3fa6ca1c61f383464aaf2bb0e2f8e772a1f01d486832464de363b951"
dependencies = [
 "num-traits",
 "rand 0.8.5",
 "serde",
]

[[package]]
name = "ordered-float"
version = "5.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2c1f9f56e534ac6a9b8a4600bdf0f530fb393b5f393e7b4d03489c3cf0c3f01"
dependencies = [
 "num-traits",
]

[[package]]
name = "ordered-multimap"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49203cdcae0030493bad186b28da2fa25645fa276a51b6fec8010d281e02ef79"
dependencies = [
 "dlv-list",
 "hashbrown 0.14.5",
]

[[package]]
name = "outref"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a80800c0488c3a21695ea981a54918fbb37abf04f4d0720c453632255e2ff0e"

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "ownedbytes"
version = "0.7.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "stable_deref_trait",
]

[[package]]
name = "owo-colors"
version = "4.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1036865bb9422d3300cf723f657c2851d0e9ab12567854b1f4eba3d77decf564"

[[package]]
name = "p256"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9863ad85fa8f4460f9c48cb909d38a0d689dba1f6f6988a5e3e0d31071bcd4b"
dependencies = [
 "ecdsa",
 "elliptic-curve",
 "primeorder",
 "sha2",
]

[[package]]
name = "p384"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe42f1670a52a47d448f14b6a5c61dd78fce51856e68edaa38f7ae3a46b8d6b6"
dependencies = [
 "ecdsa",
 "elliptic-curve",
 "primeorder",
 "sha2",
]

[[package]]
name = "parking"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f38d5652c16fde515bb1ecef450ab0f6a219d619a7274976324d5e377f7dceba"

[[package]]
name = "parking_lot"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.6",
]

[[package]]
name = "parking_lot"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bf18183cf54e8d6059647fc3063646a1801cf30896933ec2311622cc4b9a27"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.10",
]

[[package]]
name = "parking_lot_core"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc"
dependencies = [
 "cfg-if",
 "instant",
 "libc",
 "redox_syscall 0.2.16",
 "smallvec",
 "winapi",
]

[[package]]
name = "parking_lot_core"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e401f977ab385c9e4e3ab30627d6f26d00e2c73eef317493c4ec6d468726cf8"
dependencies = [
 "cfg-if",
 "libc",
 "redox_syscall 0.5.12",
 "smallvec",
 "windows-targets 0.52.6",
]

[[package]]
name = "parquet"
version = "55.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be7b2d778f6b841d37083ebdf32e33a524acde1266b5884a8ca29bf00dfa1231"
dependencies = [
 "ahash 0.8.12",
 "arrow-array",
 "arrow-buffer",
 "arrow-cast",
 "arrow-data",
 "arrow-ipc",
 "arrow-schema",
 "arrow-select",
 "base64 0.22.1",
 "brotli 8.0.1",
 "bytes",
 "chrono",
 "flate2",
 "futures",
 "half",
 "hashbrown 0.15.3",
 "lz4_flex",
 "num",
 "num-bigint",
 "object_store",
 "paste",
 "seq-macro",
 "simdutf8",
 "snap",
 "thrift",
 "tokio",
 "twox-hash 2.1.0",
 "zstd 0.13.3",
]

[[package]]
name = "parse-display"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "914a1c2265c98e2446911282c6ac86d8524f495792c38c5bd884f80499c7538a"
dependencies = [
 "parse-display-derive",
 "regex",
 "regex-syntax 0.8.5",
]

[[package]]
name = "parse-display-derive"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ae7800a4c974efd12df917266338e79a7a74415173caf7e70aa0a0707345281"
dependencies = [
 "proc-macro2",
 "quote",
 "regex",
 "regex-syntax 0.8.5",
 "structmeta",
 "syn 2.0.101",
]

[[package]]
name = "parse-zoneinfo"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f2a05b18d44e2957b88f96ba460715e295bc1d7510468a2f3d3b44535d26c24"
dependencies = [
 "regex",
]

[[package]]
name = "passwords"
version = "3.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11407193a7c2bd14ec6b0ec3394da6fdcf7a4d5dcbc8c3cc38dfb17802c8d59c"
dependencies = [
 "random-pick",
]

[[package]]
name = "paste"
version = "1.0.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57c0d7b74b563b49d38dae00a0c37d4d6de9b432382b2892f0574ddcae73fd0a"

[[package]]
name = "pbkdf2"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ed6a7761f76e3b9f92dfb0a60a6a6477c61024b775147ff0973a02653abaf2"
dependencies = [
 "digest",
 "hmac",
]

[[package]]
name = "pem"
version = "3.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38af38e8470ac9dee3ce1bae1af9c1671fffc44ddfd8bd1d0a3445bf349a8ef3"
dependencies = [
 "base64 0.22.1",
 "serde",
]

[[package]]
name = "pem-rfc7468"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88b39c9bfcfc231068454382784bb460aae594343fb030d46e9f50a645418412"
dependencies = [
 "base64ct",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "petgraph"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4c5cc86750666a3ed20bdaf5ca2a0344f9c67674cae0515bec2da16fbaa47db"
dependencies = [
 "fixedbitset 0.4.2",
 "indexmap 2.9.0",
 "serde",
 "serde_derive",
]

[[package]]
name = "petgraph"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3672b37090dbd86368a4145bc067582552b29c27377cad4e0a306c97f9bd7772"
dependencies = [
 "fixedbitset 0.5.7",
 "indexmap 2.9.0",
]

[[package]]
name = "phf"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd6780a80ae0c52cc120a26a1a42c1ae51b247a253e4e06113d23d2c2edd078"
dependencies = [
 "phf_shared",
]

[[package]]
name = "phf_codegen"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aef8048c789fa5e851558d709946d6d79a8ff88c0440c587967f8e94bfb1216a"
dependencies = [
 "phf_generator",
 "phf_shared",
]

[[package]]
name = "phf_generator"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c80231409c20246a13fddb31776fb942c38553c51e871f8cbd687a4cfb5843d"
dependencies = [
 "phf_shared",
 "rand 0.8.5",
]

[[package]]
name = "phf_shared"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67eabc2ef2a60eb7faa00097bd1ffdb5bd28e62bf39990626a582201b7a754e5"
dependencies = [
 "siphasher 1.0.1",
]

[[package]]
name = "pilota"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d18f400b02a75df232f315ad31c6d15b1b649237151ebe8b242eee1657330137"
dependencies = [
 "ahash 0.8.12",
 "anyhow",
 "async-recursion",
 "bytes",
 "derivative",
 "faststr",
 "integer-encoding 4.0.2",
 "lazy_static",
 "linkedbytes",
 "ordered-float 4.6.0",
 "paste",
 "serde",
 "smallvec",
 "thiserror 1.0.69",
 "tokio",
]

[[package]]
name = "pin-project"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677f1add503faace112b9f1373e43e9e054bfdd22ff1a63c1bc485eaec6a6a8a"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e918e4ff8c4549eb882f14b3a4bc8c8bc93de829416eacf579f1207a8fbf861"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pin-project-lite"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b3cff922bd51709b605d9ead9aa71031d81447142d828eb4a6eba76fe619f9b"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "piper"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96c8c490f422ef9a4efd2cb5b42b76c8613d7e7dfc1caf667b8a3350a5acc066"
dependencies = [
 "atomic-waker",
 "fastrand",
 "futures-io",
]

[[package]]
name = "pkcs1"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8ffb9f10fa047879315e6625af03c164b16962a5368d724ed16323b68ace47f"
dependencies = [
 "der",
 "pkcs8",
 "spki",
]

[[package]]
name = "pkcs5"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e847e2c91a18bfa887dd028ec33f2fe6f25db77db3619024764914affe8b69a6"
dependencies = [
 "aes",
 "cbc",
 "der",
 "pbkdf2",
 "scrypt",
 "sha2",
 "spki",
]

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der",
 "pkcs5",
 "rand_core 0.6.4",
 "spki",
]

[[package]]
name = "pkg-config"
version = "0.3.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7edddbd0b52d732b21ad9a5fab5c704c14cd949e5e9a1ec5929a24fded1b904c"

[[package]]
name = "poem"
version = "3.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45d6156bc3d60b0e1ce2cceb9d6de2f0853b639173a05f6c4ed224bee0d2ef2e"
dependencies = [
 "async-compression 0.4.23",
 "bytes",
 "chrono",
 "cookie",
 "futures-util",
 "headers",
 "http 1.3.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "mime",
 "multer",
 "nix 0.29.0",
 "openssl",
 "parking_lot 0.12.3",
 "percent-encoding",
 "pin-project-lite",
 "poem-derive",
 "regex",
 "rfc7239",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "smallvec",
 "sync_wrapper",
 "thiserror 2.0.12",
 "time",
 "tokio",
 "tokio-openssl",
 "tokio-util",
 "tracing",
 "wildmatch",
]

[[package]]
name = "poem-derive"
version = "3.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c1924cc95d22ee595117635c5e7b8659e664638399177d5a527e1edfd8c301d"
dependencies = [
 "proc-macro-crate 3.3.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "opaque-debug",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "350e9b48cbc6b0e028b0473b114454c6316e57336ee184ceab6e53f72c178b3e"

[[package]]
name = "portable-atomic-util"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8a2f0d8d040d7848a709caf78912debcc3f33ee4b3cac47d73d1e1069e83507"
dependencies = [
 "portable-atomic",
]

[[package]]
name = "postcard"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "170a2601f67cc9dba8edd8c4870b15f71a6a2dc196daec8c83f72b59dff628a8"
dependencies = [
 "cobs",
 "embedded-io 0.4.0",
 "embedded-io 0.6.1",
 "serde",
]

[[package]]
name = "potential_utf"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5a7c30837279ca13e7c867e9e40053bc68740f988cb07f7ca6df43cc734b585"
dependencies = [
 "zerovec",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "pprof"
version = "0.15.0"
source = "git+https://github.com/datafuse-extras/pprof-rs?rev=fe22b23#fe22b23cfa294122547a1add1377918aca0493b6"
dependencies = [
 "aligned-vec",
 "backtrace",
 "cfg-if",
 "findshlibs",
 "inferno",
 "libc",
 "log",
 "nix 0.26.4",
 "once_cell",
 "protobuf",
 "protobuf-codegen",
 "smallvec",
 "spin 0.10.0",
 "symbolic-demangle",
 "tempfile",
 "thiserror 2.0.12",
]

[[package]]
name = "ppv-lite86"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85eae3c4ed2f50dcfe72643da4befc30deadb458a9b590d720cde2f2b1e97da9"
dependencies = [
 "zerocopy",
]

[[package]]
name = "pratt"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17e0a4425d076f0718b820673a38fbf3747080c61017eeb0dd79bc7e472b8bb8"

[[package]]
name = "predicates"
version = "2.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59230a63c37f3e18569bdb90e4a89cbf5bf8b06fea0b84e65ea10cc4df47addd"
dependencies = [
 "difflib",
 "float-cmp",
 "itertools 0.10.5",
 "normalize-line-endings",
 "predicates-core",
 "regex",
]

[[package]]
name = "predicates-core"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "727e462b119fe9c93fd0eb1429a5f7647394014cf3c04ab2c0350eeb09095ffa"

[[package]]
name = "predicates-tree"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72dd2d6d381dfb73a193c7fca536518d7caee39fc8503f74e7dc0be0531b425c"
dependencies = [
 "predicates-core",
 "termtree",
]

[[package]]
name = "pretty_assertions"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ae130e2f271fbc2ac3a40fb1d07180839cdbbe443c7a27e1e3c13c5cac0116d"
dependencies = [
 "diff",
 "yansi",
]

[[package]]
name = "prettyplease"
version = "0.2.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "664ec5419c51e34154eec046ebcba56312d5a2fc3b09a06da188e1ad21afadf6"
dependencies = [
 "proc-macro2",
 "syn 2.0.101",
]

[[package]]
name = "primeorder"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "353e1ca18966c16d9deb1c69278edbc5f194139612772bd9537af60ac231e1e6"
dependencies = [
 "elliptic-curve",
]

[[package]]
name = "proc-macro-crate"
version = "1.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f4c021e1093a56626774e81216a4ce732a735e5bad4868a03f3ed65ca0c3919"
dependencies = [
 "once_cell",
 "toml_edit 0.19.15",
]

[[package]]
name = "proc-macro-crate"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edce586971a4dfaa28950c6f18ed55e0406c1ab88bbce2c6f6293a7aaba73d35"
dependencies = [
 "toml_edit 0.22.26",
]

[[package]]
name = "proc-macro-error"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18f33027081eba0a6d8aba6d1b1c3a3be58cbb12106341c2d5759fcd9b5277e7"
dependencies = [
 "proc-macro-error-attr 0.4.12",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr 1.0.4",
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a5b4b77fdb63c1eca72173d68d24501c54ab1269409f6b672c85deb18af69de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
 "syn-mid",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2",
 "quote",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96de42df36bb9bba5542fe9f1a054b8cc87e172759a1868aa05c1f3acc89dfc5"
dependencies = [
 "proc-macro2",
 "quote",
]

[[package]]
name = "proc-macro-error2"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11ec05c52be0a07b08061f7dd003e7d7092e0472bc731b4af7bb1ef876109802"
dependencies = [
 "proc-macro-error-attr2",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "proc-macro-hack"
version = "0.5.20+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068"

[[package]]
name = "proc-macro2"
version = "1.0.95"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02b3e5e68a3a1a02aad3ec490a98007cbc13c37cbe84a3cd7b8e406d76e7f778"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "procfs"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc5b72d8145275d844d4b5f6d4e1eef00c8cd889edb6035c21675d1bb1f45c9f"
dependencies = [
 "bitflags 2.9.0",
 "chrono",
 "flate2",
 "hex",
 "procfs-core",
 "rustix 0.38.44",
]

[[package]]
name = "procfs-core"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "239df02d8349b06fc07398a3a1697b06418223b1c7725085e801e7c0fc6a12ec"
dependencies = [
 "bitflags 2.9.0",
 "chrono",
 "hex",
]

[[package]]
name = "prodash"
version = "28.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "744a264d26b88a6a7e37cbad97953fa233b94d585236310bcbc88474b4092d79"
dependencies = [
 "bytesize",
 "human_format",
]

[[package]]
name = "proj4rs"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fed6bd261b7a9ec9308845b375e7d8bcf19394b30d2fcc150bd9c04b2fd14788"
dependencies = [
 "console_log",
 "crs-definitions",
 "geo-types",
 "js-sys",
 "lazy_static",
 "thiserror 1.0.69",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "prometheus-client"
version = "0.22.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "504ee9ff529add891127c4827eb481bd69dc0ebc72e9a682e187db4caa60c3ca"
dependencies = [
 "dtoa",
 "itoa",
 "parking_lot 0.12.3",
 "prometheus-client-derive-encode",
]

[[package]]
name = "prometheus-client"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf41c1a7c32ed72abe5082fb19505b969095c12da9f5732a4bc9878757fd087c"
dependencies = [
 "dtoa",
 "itoa",
 "parking_lot 0.12.3",
 "prometheus-client-derive-encode",
]

[[package]]
name = "prometheus-client-derive-encode"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "440f724eba9f6996b75d63681b0a92b06947f1457076d503a4d2e2c8f56442b8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prometheus-parse"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "811031bea65e5a401fb2e1f37d802cca6601e204ac463809a3189352d13b78a5"
dependencies = [
 "chrono",
 "itertools 0.12.1",
 "once_cell",
 "regex",
]

[[package]]
name = "proptest"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14cae93065090804185d3b75f0bf93b8eeda30c7a9b4a33d3bdb3988d6229e50"
dependencies = [
 "bitflags 2.9.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_xorshift",
 "regex-syntax 0.8.5",
 "unarray",
]

[[package]]
name = "prost"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2796faa41db3ec313a31f7624d9286acf277b52de526150b7e69f3debf891ee5"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-build"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be769465445e8c1474e9c5dac2018218498557af32d9ed057325ec9a41ae81bf"
dependencies = [
 "heck 0.5.0",
 "itertools 0.14.0",
 "log",
 "multimap",
 "once_cell",
 "petgraph 0.7.1",
 "prettyplease",
 "prost",
 "prost-types",
 "regex",
 "syn 2.0.101",
 "tempfile",
]

[[package]]
name = "prost-derive"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a56d757972c98b346a9b766e3f02746cde6dd1cd1d1d563472929fdd74bec4d"
dependencies = [
 "anyhow",
 "itertools 0.14.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "prost-types"
version = "0.13.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52c2c1bf36ddb1a1c396b3601a3cec27c2462e45f07c386894ec3ccf5332bd16"
dependencies = [
 "prost",
]

[[package]]
name = "protobuf"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d65a1d4ddae7d8b5de68153b48f6aa3bba8cb002b243dbdbc55a5afbc98f99f4"
dependencies = [
 "once_cell",
 "protobuf-support",
 "thiserror 1.0.69",
]

[[package]]
name = "protobuf-codegen"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d3976825c0014bbd2f3b34f0001876604fe87e0c86cd8fa54251530f1544ace"
dependencies = [
 "anyhow",
 "once_cell",
 "protobuf",
 "protobuf-parse",
 "regex",
 "tempfile",
 "thiserror 1.0.69",
]

[[package]]
name = "protobuf-parse"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4aeaa1f2460f1d348eeaeed86aea999ce98c1bded6f089ff8514c9d9dbdc973"
dependencies = [
 "anyhow",
 "indexmap 2.9.0",
 "log",
 "protobuf",
 "protobuf-support",
 "tempfile",
 "thiserror 1.0.69",
 "which",
]

[[package]]
name = "protobuf-support"
version = "3.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e36c2f31e0a47f9280fb347ef5e461ffcd2c52dd520d8e216b52f93b0b0d7d6"
dependencies = [
 "thiserror 1.0.69",
]

[[package]]
name = "prqlc"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4beb05b6b71ce096fa56d73006ab1c42a8d11bf190d193fa511a134f7730ec43"
dependencies = [
 "anstream",
 "anyhow",
 "ariadne",
 "atty",
 "chrono",
 "clap",
 "clap_complete",
 "clap_complete_command",
 "clio",
 "color-eyre",
 "colorchoice-clap",
 "csv",
 "enum-as-inner",
 "env_logger 0.10.2",
 "itertools 0.12.1",
 "log",
 "minijinja",
 "notify",
 "once_cell",
 "prqlc-ast",
 "prqlc-parser",
 "regex",
 "semver",
 "serde",
 "serde_json",
 "serde_yaml",
 "sqlformat",
 "sqlparser 0.43.1",
 "strum 0.26.3",
 "strum_macros 0.26.4",
 "walkdir",
]

[[package]]
name = "prqlc-ast"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c98923b046bc48046e3846b14a5fde5a059f681c7c367bd0ab96ebd3ecc33a71"
dependencies = [
 "anyhow",
 "enum-as-inner",
 "semver",
 "serde",
 "strum 0.26.3",
]

[[package]]
name = "prqlc-parser"
version = "0.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "855ad9aba599ef608efc88a30ebd731155997d9bbe780639eb175de060b6cddc"
dependencies = [
 "chumsky",
 "itertools 0.12.1",
 "prqlc-ast",
 "semver",
 "stacker",
]

[[package]]
name = "psl-types"
version = "2.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33cb294fe86a74cbcf50d4445b37da762029549ebeea341421c7c70370f86cac"

[[package]]
name = "psm"
version = "0.1.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e944464ec8536cd1beb0bbfd96987eb5e3b72f2ecdafdc5c769a37f1fa2ae1f"
dependencies = [
 "cc",
]

[[package]]
name = "ptr_meta"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0738ccf7ea06b608c10564b31debd4f5bc5e197fc8bfe088f68ae5ce81e7a4f1"
dependencies = [
 "ptr_meta_derive 0.1.4",
]

[[package]]
name = "ptr_meta"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe9e76f66d3f9606f44e45598d155cb13ecf09f4a28199e48daf8c8fc937ea90"
dependencies = [
 "ptr_meta_derive 0.3.0",
]

[[package]]
name = "ptr_meta_derive"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16b845dbfca988fa33db069c0e230574d15a3088f147a87b64c7589eb662c9ac"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "ptr_meta_derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca414edb151b4c8d125c12566ab0d74dc9cdba36fb80eb7b848c15f495fd32d1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "publicsuffix"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42ea446cab60335f76979ec15e12619a2165b5ae2c12166bef27d283a9fadf"
dependencies = [
 "idna",
 "psl-types",
]

[[package]]
name = "pulley-interpreter"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a3b8d81cf799e20564931e9867ca32de545188c6ee4c2e0f6e41d32f0c7dc6fb"
dependencies = [
 "cranelift-bitset",
 "log",
 "sptr",
]

[[package]]
name = "pyo3"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5203598f366b11a02b13aa20cab591229ff0a89fd121a308a5df751d5fc9219"
dependencies = [
 "cfg-if",
 "indoc",
 "libc",
 "memoffset",
 "once_cell",
 "portable-atomic",
 "pyo3-build-config",
 "pyo3-ffi",
 "pyo3-macros",
 "unindent",
]

[[package]]
name = "pyo3-build-config"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99636d423fa2ca130fa5acde3059308006d46f98caac629418e53f7ebb1e9999"
dependencies = [
 "once_cell",
 "python3-dll-a",
 "target-lexicon 0.13.2",
]

[[package]]
name = "pyo3-ffi"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78f9cf92ba9c409279bc3305b5409d90db2d2c22392d443a87df3a1adad59e33"
dependencies = [
 "libc",
 "pyo3-build-config",
]

[[package]]
name = "pyo3-macros"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b999cb1a6ce21f9a6b147dcf1be9ffedf02e0043aec74dc390f3007047cecd9"
dependencies = [
 "proc-macro2",
 "pyo3-macros-backend",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "pyo3-macros-backend"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "822ece1c7e1012745607d5cf0bcb2874769f0f7cb34c4cde03b9358eb9ef911a"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "pyo3-build-config",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "python3-dll-a"
version = "0.2.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49fe4227a288cf9493942ad0220ea3f185f4d1f2a14f197f7344d6d02f4ed4ed"
dependencies = [
 "cc",
]

[[package]]
name = "quad-rand"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a651516ddc9168ebd67b24afd085a718be02f8858fe406591b013d101ce2f40"

[[package]]
name = "quick-xml"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f50b1c63b38611e7d4d7f68b82d3ad0cc71a2ad2e7f61fc10f1328d917c93cd"
dependencies = [
 "memchr",
]

[[package]]
name = "quick-xml"
version = "0.37.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "331e97a1af0bf59823e6eadffe373d7b27f485be8748f71471c662c1f269b7fb"
dependencies = [
 "memchr",
 "serde",
]

[[package]]
name = "quickcheck"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "588f6378e4dd99458b60ec275b4477add41ce4fa9f64dcba6f15adccb19b50d6"
dependencies = [
 "env_logger 0.8.4",
 "log",
 "rand 0.8.5",
]

[[package]]
name = "quinn"
version = "0.11.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "626214629cda6781b6dc1d316ba307189c85ba657213ce642d9c77670f8202c8"
dependencies = [
 "bytes",
 "cfg_aliases 0.2.1",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash 2.1.1",
 "rustls 0.23.27",
 "socket2",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-proto"
version = "0.11.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49df843a9161c85bb8aae55f101bc0bac8bcafd637a620d9122fd7e0b2f7422e"
dependencies = [
 "bytes",
 "getrandom 0.3.3",
 "lru-slab",
 "rand 0.9.1",
 "ring",
 "rustc-hash 2.1.1",
 "rustls 0.23.27",
 "rustls-pki-types",
 "slab",
 "thiserror 2.0.12",
 "tinyvec",
 "tracing",
 "web-time",
]

[[package]]
name = "quinn-udp"
version = "0.5.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee4e529991f949c5e25755532370b8af5d114acae52326361d68d47af64aa842"
dependencies = [
 "cfg_aliases 0.2.1",
 "libc",
 "once_cell",
 "socket2",
 "tracing",
 "windows-sys 0.59.0",
]

[[package]]
name = "quote"
version = "1.0.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1885c039570dc00dcb4ff087a89e185fd56bae234ddc7f056a945bf36467248d"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5"

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "radix_trie"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c069c179fcdc6a2fe24d8d18305cf085fdbd4f922c041943e203685d6a1c58fd"
dependencies = [
 "endian-type",
 "nibble_vec",
]

[[package]]
name = "raft-log"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81c367dae1b209da86ab7fe42139db9652eabddbe4dabacf5c76137cd709521c"
dependencies = [
 "byteorder",
 "clap",
 "codeq",
 "fs2",
 "log",
 "thiserror 1.0.69",
]

[[package]]
name = "rancor"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "caf5f7161924b9d1cea0e4cabc97c372cea92b5f927fc13c6bca67157a0ad947"
dependencies = [
 "ptr_meta 0.3.0",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
 "serde",
]

[[package]]
name = "rand"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fbfd9d094a40bf3ae768db9361049ace4c0e04a4fd6b359518bd7b73a73dd97"
dependencies = [
 "rand_chacha 0.9.0",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3022b5f1df60f26e1ffddd6c66e8aa15de382ae63b3a0c1bfc0e4d3e3f325cb"
dependencies = [
 "ppv-lite86",
 "rand_core 0.9.3",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.16",
 "serde",
]

[[package]]
name = "rand_core"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99d9a13982dcf210057a8a78572b2217b667c3beacbf3a0d8b454f6f82837d38"
dependencies = [
 "getrandom 0.3.3",
]

[[package]]
name = "rand_distr"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32cb0b9bc82b0a0876c2dd994a7e7a2683d3e7390ca40e6886785ef0c7e3ee31"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "rand_xoshiro"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f97cdb2a36ed4183de61b2f824cc45c9f1037f28afe0a322e9fff4c108b5aaa"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "random-number"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fc8cdd49be664772ffc3dbfa743bb8c34b78f9cc6a9f50e56ae878546796067"
dependencies = [
 "proc-macro-hack",
 "rand 0.8.5",
 "random-number-macro-impl",
]

[[package]]
name = "random-number-macro-impl"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5135143cb48d14289139e4615bffec0d59b4cbfd4ea2398a3770bd2abfc4aa2"
dependencies = [
 "proc-macro-hack",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "random-pick"
version = "1.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c179499072da789afe44127d5f4aa6012de2c2f96ef759990196b37387a2a0f8"
dependencies = [
 "random-number",
]

[[package]]
name = "rawpointer"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a357793950651c4ed0f3f52338f53b2f809f32d83a07f72909fa13e4c6c1e3"

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "recursive"
version = "0.1.1"
source = "git+https://github.com/datafuse-extras/recursive.git?rev=6af35a1#6af35a1e59e7050f86ee19fbd0a79535d016c87d"
dependencies = [
 "recursive-proc-macro-impl",
 "stacker",
]

[[package]]
name = "recursive-proc-macro-impl"
version = "0.1.1"
source = "git+https://github.com/datafuse-extras/recursive.git?rev=6af35a1#6af35a1e59e7050f86ee19fbd0a79535d016c87d"
dependencies = [
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "redis"
version = "0.27.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09d8f99a4090c89cc489a94833c901ead69bfbf3877b4867d5482e321ee875bc"
dependencies = [
 "arc-swap",
 "async-trait",
 "backon",
 "bytes",
 "combine",
 "futures",
 "futures-util",
 "itertools 0.13.0",
 "itoa",
 "num-bigint",
 "percent-encoding",
 "pin-project-lite",
 "ryu",
 "sha1_smol",
 "socket2",
 "tokio",
 "tokio-util",
 "url",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567664f262709473930a4bf9e51bf2ebf3348f2e748ccc50dea20646858f8f29"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.5.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "928fca9cf2aa042393a8325b9ead81d2f0df4cb12e1e24cef072922ccd99c5af"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "redox_users"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba009ff324d1fc1b900bd1fdb31564febe58a8ccc8a6fdbb93b543d33b13ca43"
dependencies = [
 "getrandom 0.2.16",
 "libredox",
 "thiserror 1.0.69",
]

[[package]]
name = "ref-cast"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a0ae411dbe946a674d89546582cea4ba2bb8defac896622d6496f14c23ba5cf"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1165225c21bff1f3bbce98f5a1f889949bc902d3575308cc7b0de30b4f6d27c7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "regalloc2"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12908dbeb234370af84d0579b9f68258a0f67e201412dd9a2814e6f45b2fc0f0"
dependencies = [
 "hashbrown 0.14.5",
 "log",
 "rustc-hash 2.1.1",
 "slice-group-by",
 "smallvec",
]

[[package]]
name = "regex"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b544ef1b4eac5dc2db33ea63606ae9ffcfac26c1416a2806ae0bf5f56b201191"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-automata 0.4.9",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "809e8dc61f6de73b46c85f4c96486310fe304c434cfa43669d7b40f711150908"
dependencies = [
 "aho-corasick",
 "memchr",
 "regex-syntax 0.8.5",
]

[[package]]
name = "regex-lite"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53a49587ad06b26609c52e423de037e7f57f20d53535d66e08c695f347df952a"

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b15c43186be67a4fd63bee50d0303afffcef381492ebe2c5d87f324e1b8815c"

[[package]]
name = "relative-path"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba39f3699c378cd8970968dcbff9c43159ea4cfbd88d43c00b22f2ef10a435d2"

[[package]]
name = "rend"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71fe3824f5629716b1589be05dacd749f6aa084c87e00e016714a8cdfccc997c"
dependencies = [
 "bytecheck",
]

[[package]]
name = "rend"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a35e8a6bf28cd121053a66aa2e6a2e3eaffad4a60012179f0e864aa5ffeff215"

[[package]]
name = "replace_with"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3a8614ee435691de62bcffcf4a66d91b3594bf1428a5722e79103249a095690"

[[package]]
name = "reqsign"
version = "0.16.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43451dbf3590a7590684c25fb8d12ecdcc90ed3ac123433e500447c7d77ed701"
dependencies = [
 "anyhow",
 "async-trait",
 "base64 0.22.1",
 "chrono",
 "form_urlencoded",
 "getrandom 0.2.16",
 "hex",
 "hmac",
 "home",
 "http 1.3.1",
 "jsonwebtoken",
 "log",
 "once_cell",
 "percent-encoding",
 "quick-xml 0.37.5",
 "rand 0.8.5",
 "reqwest",
 "rsa",
 "rust-ini",
 "serde",
 "serde_json",
 "sha1",
 "sha2",
 "tokio",
]

[[package]]
name = "reqwest"
version = "0.12.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d19c46a6fdd48bc4dab94b6103fccc55d34c67cc0ad04653aad4ea2a07cd7bbb"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "cookie",
 "cookie_store",
 "encoding_rs",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2 0.4.10",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-rustls 0.27.5",
 "hyper-tls",
 "hyper-util",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "native-tls",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "quinn",
 "rustls 0.23.27",
 "rustls-native-certs 0.8.1",
 "rustls-pemfile 2.2.0",
 "rustls-pki-types",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper",
 "system-configuration",
 "tokio",
 "tokio-native-tls",
 "tokio-rustls 0.26.2",
 "tokio-util",
 "tower 0.5.2",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "wasm-streams",
 "web-sys",
 "webpki-roots 0.26.11",
 "windows-registry",
]

[[package]]
name = "reqwest-hickory-resolver"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4466c6958bbbd3024ace15426f7b5369222c0ad517167d10056fd5cc76aec99b"
dependencies = [
 "hickory-resolver",
 "rand 0.9.1",
 "reqwest",
]

[[package]]
name = "resolv-conf"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95325155c684b1c89f7765e30bc1c42e4a6da51ca513615660cb8a62ef9a88e3"

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac",
 "subtle",
]

[[package]]
name = "rfc7239"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a82f1d1e38e9a85bb58ffcfadf22ed6f2c94e8cd8581ec2b0f80a2a6858350f"
dependencies = [
 "uncased",
]

[[package]]
name = "rgb"
version = "0.8.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57397d16646700483b67d2dd6511d79318f9d057fdbd21a4066aeac8b41d310a"
dependencies = [
 "bytemuck",
]

[[package]]
name = "ring"
version = "0.17.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4689e6c2294d81e88dc6261c768b63bc4fcdb852be6d1352498b114f61383b7"
dependencies = [
 "cc",
 "cfg-if",
 "getrandom 0.2.16",
 "libc",
 "untrusted",
 "windows-sys 0.52.0",
]

[[package]]
name = "ringbuffer"
version = "0.14.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4eba9638e96ac5a324654f8d47fb71c5e21abef0f072740ed9c1d4b0801faa37"

[[package]]
name = "rio"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e98c25665909853c07874301124482754434520ab572ac6a22e90366de6685b"
dependencies = [
 "libc",
]

[[package]]
name = "rkyv"
version = "0.7.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9008cd6385b9e161d8229e1f6549dd23c3d022f132a2ea37ac3a10ac4935779b"
dependencies = [
 "bitvec",
 "bytecheck",
 "bytes",
 "hashbrown 0.12.3",
 "ptr_meta 0.1.4",
 "rend 0.4.2",
 "rkyv_derive 0.7.45",
 "seahash",
 "tinyvec",
 "uuid",
]

[[package]]
name = "rkyv"
version = "0.8.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e147371c75553e1e2fcdb483944a8540b8438c31426279553b9a8182a9b7b65"
dependencies = [
 "bytes",
 "hashbrown 0.15.3",
 "indexmap 2.9.0",
 "munge",
 "ptr_meta 0.3.0",
 "rancor",
 "rend 0.5.2",
 "rkyv_derive 0.8.10",
 "tinyvec",
 "uuid",
]

[[package]]
name = "rkyv_derive"
version = "0.7.45"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "503d1d27590a2b0a3a4ca4c94755aa2875657196ecbf401a42eff41d7de532c0"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "rkyv_derive"
version = "0.8.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "246b40ac189af6c675d124b802e8ef6d5246c53e17367ce9501f8f66a81abb7a"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "rle-decode-fast"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3582f63211428f83597b51b2ddb88e2a91a9d52d12831f9d08f5e624e8977422"

[[package]]
name = "rmp"
version = "0.8.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "228ed7c16fa39782c3b3468e974aec2795e9089153cd08ee2e9aefb3613334c4"
dependencies = [
 "byteorder",
 "num-traits",
 "paste",
]

[[package]]
name = "rmp-serde"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52e599a477cf9840e92f2cde9a7189e67b42c57532749bf90aea6ec10facd4db"
dependencies = [
 "byteorder",
 "rmp",
 "serde",
]

[[package]]
name = "roaring"
version = "0.10.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19e8d2cfa184d94d0726d650a9f4a1be7f9b76ac9fdb954219878dc00c1c1e7b"
dependencies = [
 "bytemuck",
 "byteorder",
 "serde",
]

[[package]]
name = "robust"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e27ee8bb91ca0adcf0ecb116293afa12d393f9c2b9b9cd54d33e8078fe19839"

[[package]]
name = "rotbl"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "751d6483053228f9e4102fb1e3054ac727b3bde446c4abf02663220cd1bab332"
dependencies = [
 "anyhow",
 "bincode 2.0.1",
 "byteorder",
 "bytes",
 "clap",
 "crc32fast",
 "futures",
 "futures-async-stream",
 "lru-cache-map",
 "num-format",
 "serde",
 "serde_json",
 "tokio",
]

[[package]]
name = "rquickjs"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9cbd33e0b668aea0ab238b9164523aca929096f9f40834700d71d91dd4888882"
dependencies = [
 "rquickjs-core",
 "rquickjs-macro",
]

[[package]]
name = "rquickjs-core"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e9129d69b7b8f7ee8ad1da5b12c7f4a8a8acd45f2e6dd9cb2ee1bc5a1f2fa3d"
dependencies = [
 "async-lock",
 "relative-path",
 "rquickjs-sys",
]

[[package]]
name = "rquickjs-macro"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7d2ecaf7c9eda262e02a91e9541989a9dd18984d17d0d97f99f33b464318057"
dependencies = [
 "convert_case 0.6.0",
 "fnv",
 "ident_case",
 "indexmap 2.9.0",
 "proc-macro-crate 1.3.1",
 "proc-macro-error 1.0.4",
 "proc-macro2",
 "quote",
 "rquickjs-core",
 "syn 2.0.101",
]

[[package]]
name = "rquickjs-sys"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf6f2288d8e7fbb5130f62cf720451641e99d55f6fde9db86aa2914ecb553fd2"
dependencies = [
 "cc",
]

[[package]]
name = "rsa"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78928ac1ed176a5ca1d17e578a1825f3d81ca54cf41053a592584b020cfd691b"
dependencies = [
 "const-oid",
 "digest",
 "num-bigint-dig",
 "num-integer",
 "num-traits",
 "pkcs1",
 "pkcs8",
 "rand_core 0.6.4",
 "sha2",
 "signature",
 "spki",
 "subtle",
 "zeroize",
]

[[package]]
name = "rspack-codespan-reporting"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc53b3a0e58f509a8b55bde278d44c05879f27a66819346e0fef193c6348e9f8"
dependencies = [
 "termcolor",
 "unicode-width 0.1.14",
]

[[package]]
name = "rstar"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "421400d13ccfd26dfa5858199c30a5d76f9c54e0dba7575273025b43c5175dbb"
dependencies = [
 "heapless",
 "num-traits",
 "smallvec",
]

[[package]]
name = "rtrb"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad8388ea1a9e0ea807e442e8263a699e7edcb320ecbcd21b4fa8ff859acce3ba"

[[package]]
name = "rust-ini"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e310ef0e1b6eeb79169a1171daf9abcb87a2e17c03bee2c4bb100b55c75409f"
dependencies = [
 "cfg-if",
 "ordered-multimap",
 "trim-in-place",
]

[[package]]
name = "rust-stemmers"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e46a2036019fdb888131db7a4c847a1063a7493f971ed94ea82c67eada63ca54"
dependencies = [
 "serde",
 "serde_derive",
]

[[package]]
name = "rust_decimal"
version = "1.37.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faa7de2ba56ac291bd90c6b9bece784a52ae1411f9506544b3eae36dd2356d50"
dependencies = [
 "arrayvec",
 "borsh",
 "bytes",
 "num-traits",
 "rand 0.8.5",
 "rkyv 0.7.45",
 "serde",
 "serde_json",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hash"
version = "2.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "357703d41365b4b27c590e3ed91eabb1b663f07c4c084095e60cbed4362dff0d"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "rustc_version"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcb3a22ef46e85b45de6ee7e79d063319ebb6594faafcf1c225ea92ab6e9b92"
dependencies = [
 "semver",
]

[[package]]
name = "rustix"
version = "0.38.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdb5bc1ae2baa591800df16c9ca78619bf65c0488b41b96ccec5d11220d8c154"
dependencies = [
 "bitflags 2.9.0",
 "errno",
 "libc",
 "linux-raw-sys 0.4.15",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c71e83d6afe7ff64890ec6b71d6a69bb8a610ab78ce364b3352876bb4c801266"
dependencies = [
 "bitflags 2.9.0",
 "errno",
 "libc",
 "linux-raw-sys 0.9.4",
 "windows-sys 0.59.0",
]

[[package]]
name = "rustix-linux-procfs"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2fc84bf7e9aa16c4f2c758f27412dc9841341e16aa682d9c7ac308fe3ee12056"
dependencies = [
 "once_cell",
 "rustix 1.0.7",
]

[[package]]
name = "rustls"
version = "0.21.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f56a14d1f48b391359b22f731fd4bd7e43c97f3c50eee276f3aa09c94784d3e"
dependencies = [
 "log",
 "ring",
 "rustls-webpki 0.101.7",
 "sct",
]

[[package]]
name = "rustls"
version = "0.23.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "730944ca083c1c233a75c09f199e973ca499344a2b7ba9e755c457e86fb4a321"
dependencies = [
 "aws-lc-rs",
 "log",
 "once_cell",
 "ring",
 "rustls-pki-types",
 "rustls-webpki 0.103.3",
 "subtle",
 "zeroize",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile 1.0.4",
 "schannel",
 "security-framework 2.11.1",
]

[[package]]
name = "rustls-native-certs"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5bfb394eeed242e909609f56089eecfe5fda225042e8b171791b9c95f5931e5"
dependencies = [
 "openssl-probe",
 "rustls-pemfile 2.2.0",
 "rustls-pki-types",
 "schannel",
 "security-framework 2.11.1",
]

[[package]]
name = "rustls-native-certs"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcff2dd52b58a8d98a70243663a0d234c4e2b79235637849d15913394a247d3"
dependencies = [
 "openssl-probe",
 "rustls-pki-types",
 "schannel",
 "security-framework 3.2.0",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-pemfile"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dce314e5fee3f39953d46bb63bb8a46d40c2f8fb7cc5a3b6cab2bde9721d6e50"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "rustls-pki-types"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "229a4a4c221013e7e1f1a043678c5cc39fe5171437c88fb47151a21e6f5b5c79"
dependencies = [
 "web-time",
 "zeroize",
]

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "rustls-webpki"
version = "0.103.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4a72fe2bcf7a6ac6fd7d0b9e5cb68aeb7d4c0a0271730218b3e92d43b4eb435"
dependencies = [
 "aws-lc-rs",
 "ring",
 "rustls-pki-types",
 "untrusted",
]

[[package]]
name = "rustversion"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eded382c5f5f786b989652c49544c4877d9f015cc22e145a5ea8ea66c2921cd2"

[[package]]
name = "rustyline"
version = "14.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7803e8936da37efd9b6d4478277f4b2b9bb5cdb37a113e8d63222e58da647e63"
dependencies = [
 "bitflags 2.9.0",
 "cfg-if",
 "clipboard-win",
 "fd-lock",
 "home",
 "libc",
 "log",
 "memchr",
 "nix 0.28.0",
 "radix_trie",
 "unicode-segmentation",
 "unicode-width 0.1.14",
 "utf8parse",
 "windows-sys 0.52.0",
]

[[package]]
name = "ruzstd"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fad02996bfc73da3e301efe90b1837be9ed8f4a462b6ed410aa35d00381de89f"
dependencies = [
 "twox-hash 1.6.3",
]

[[package]]
name = "ryu"
version = "1.0.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28d3b2b1366ec20994f1fd18c3c594f05c5dd4bc44d8bb0c1c632c8d6829481f"

[[package]]
name = "salsa20"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97a22f5af31f73a954c10289c93e8a50cc23d971e80ee446f1f6f7137a088213"
dependencies = [
 "cipher",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "saturating"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ece8e78b2f38ec51c51f5d475df0a7187ba5111b2a28bdc761ee05b075d40a71"

[[package]]
name = "schannel"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f29ebaa345f945cec9fbbc532eb307f0fdad8161f281b6369539c8d84876b3d"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "scoped-tls"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1cf6437eb19a8f4a6cc0f7dca544973b0b78843adbfeb3683d1a94a0024a294"

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scroll"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04c565b551bafbef4157586fa379538366e4385d42082f255bfd96e4fe8519da"

[[package]]
name = "scroll"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ab8598aa408498679922eff7fa985c25d58a90771bd6be794434c5277eab1a6"

[[package]]
name = "scrypt"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0516a385866c09368f0b5bcd1caff3366aace790fcd46e2bb032697bb172fd1f"
dependencies = [
 "pbkdf2",
 "salsa20",
 "sha2",
]

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring",
 "untrusted",
]

[[package]]
name = "seahash"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c107b6f4780854c8b126e228ea8869f4d7b71260f962fefb57b996b8959ba6b"

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct",
 "der",
 "generic-array",
 "pkcs8",
 "subtle",
 "zeroize",
]

[[package]]
name = "security-framework"
version = "2.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "897b2245f0b511c87893af39b033e5ca9cce68824c4d7e7630b5a1d339658d02"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework"
version = "3.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271720403f46ca04f7ba6f55d438f8bd878d6b8ca0a1046e8228c4145bcbb316"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.10.0",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49db231d56a190491cb4aeda9527f1ad45345af50b0851622a7adb8c03b01c32"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "1.0.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56e6fa9c48d24d85fb3de5ad847117517440f6beceb7798af16b4a87d616b8d0"
dependencies = [
 "serde",
]

[[package]]
name = "seq-macro"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc711410fbe7399f390ca1c3b60ad0f53f80e95c5eb935e52268a0e2cd49acc"

[[package]]
name = "serde"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f0e2c6ed6606019b4e29e69dbaba95b11854410e5347d525002456dbbb786b6"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-bridge"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f68e451f87e0f0a51c565c907017f033ecce23a1aef9eb3faee0dfd6e578d84"
dependencies = [
 "anyhow",
 "indexmap 1.9.3",
 "serde",
]

[[package]]
name = "serde-env"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c68119a0846249fd6f4b38561b4b4727dbc4fd9fea074f1253bca7d50440ce58"
dependencies = [
 "anyhow",
 "log",
 "serde",
]

[[package]]
name = "serde_bytes"
version = "0.11.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8437fd221bde2d4ca316d61b90e337e9e702b3820b87d63caa9ba6c02bd06d96"
dependencies = [
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.219"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b0276cf7f2c73365f7157c8123c21cd9a50fbbd844757af28ca1f5925fc2a00"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_fmt"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1d4ddca14104cd60529e8c7f7ba71a2c8acd8f7f5cfcdc2faf97eeb7c3010a4"
dependencies = [
 "serde",
]

[[package]]
name = "serde_ignored"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b516445dac1e3535b6d658a7b528d771153dfb272ed4180ca4617a20550365ff"
dependencies = [
 "serde",
]

[[package]]
name = "serde_json"
version = "1.0.140"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20068b6e96dc6c9bd23e01df8827e6c7e1f2fddd43c21810382803c136b99373"
dependencies = [
 "indexmap 2.9.0",
 "itoa",
 "memchr",
 "ryu",
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "175ee3e80ae9982737ca543e96133087cbd9a485eecc3bc4de9c1a37b47ea59c"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_spanned"
version = "0.6.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87607cb1398ed59d48732e575a4c28a7a8ebf2454b964fe3f224f2afc07909e1"
dependencies = [
 "serde",
]

[[package]]
name = "serde_stacker"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69c8defe6c780725cce4ec6ad3bd91e321baf6fa4e255df1f31e345d507ef01a"
dependencies = [
 "serde",
 "stacker",
]

[[package]]
name = "serde_test"
version = "1.0.177"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f901ee573cab6b3060453d2d5f0bae4e6d628c23c0a962ff9b5f1d7c8d4f1ed"
dependencies = [
 "serde",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "3.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6b6f7f2fcb69f747921f79f3926bd1e203fce4fef62c268dd3abfb6d86029aa"
dependencies = [
 "base64 0.22.1",
 "chrono",
 "hex",
 "indexmap 1.9.3",
 "indexmap 2.9.0",
 "serde",
 "serde_derive",
 "serde_json",
 "serde_with_macros",
 "time",
]

[[package]]
name = "serde_with_macros"
version = "3.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d00caa5193a3c8362ac2b73be6b9e768aa5a4b2f721d8f4b339600c3cb51f8e"
dependencies = [
 "darling",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "serde_yaml"
version = "0.9.34+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8b1a1a2ebf674015cc02edccce75287f1a0130d394307b36743c2f5d504b47"
dependencies = [
 "indexmap 2.9.0",
 "itoa",
 "ryu",
 "serde",
 "unsafe-libyaml",
]

[[package]]
name = "serfig"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40f8bc5badc980cedcab554e3a630a5657ee53a94b274e4c937394e19186af8"
dependencies = [
 "anyhow",
 "indexmap 1.9.3",
 "log",
 "serde",
 "serde-bridge",
 "serde-env",
 "toml 0.7.8",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sha1_smol"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbfa15b3dddfee50a0fff136974b3e1bde555604ba463834a7eb7deb6417705d"

[[package]]
name = "sha2"
version = "0.10.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7507d819769d01a365ab707794a4084392c824f54a7a6a7862f8c3d0892b283"
dependencies = [
 "cfg-if",
 "cpufeatures",
 "digest",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shell-words"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24188a676b6ae68c3b2cb3a01be17fbf7240ce009799bb56d5b1409051e78fde"

[[package]]
name = "shellexpand"
version = "2.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ccc8076840c4da029af4f87e4e8daeb0fca6b87bbb02e10cb60b791450e11e4"
dependencies = [
 "dirs",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d881a16cf4426aa584979d30bd82cb33429027e42122b169753d6ef1085ed6e2"
dependencies = [
 "libc",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9203b8055f63a2a00e2f593bb0510367fe707d7ff1e5c872de2f537b339e5410"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest",
 "rand_core 0.6.4",
]

[[package]]
name = "simd-adler32"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d66dc143e6b11c1eddc06d5c423cfc97062865baf299914ab64caa38182078fe"

[[package]]
name = "simdutf8"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3a9fe34e3e7a50316060351f37187a3f546bce95496156754b601a5fa71b76e"

[[package]]
name = "similar"
version = "2.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbbb5d9659141646ae647b42fe094daf6c6192d1620870b449d9557f748b2daa"
dependencies = [
 "bstr",
 "unicode-segmentation",
]

[[package]]
name = "similar-asserts"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5b441962c817e33508847a22bd82f03a30cff43642dc2fae8b050566121eb9a"
dependencies = [
 "console",
 "similar",
]

[[package]]
name = "simple_asn1"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "297f631f50729c8c99b84667867963997ec0b50f32b2a7dbcab828ef0541e8bb"
dependencies = [
 "num-bigint",
 "num-traits",
 "thiserror 2.0.12",
 "time",
]

[[package]]
name = "simple_hll"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbdc537413bd6a291f57e2cc0a17579beb5ccaeea534e9c3001e39d9a07fa14f"
dependencies = [
 "ahash 0.8.12",
 "borsh",
 "serde",
]

[[package]]
name = "simsearch"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c869b25830e4824ef7279015cfc298a0674aca6a54eeff2efce8d12bf3701fe"
dependencies = [
 "strsim 0.10.0",
 "triple_accel",
]

[[package]]
name = "siphasher"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38b58827f4464d87d377d175e90bf58eb00fd8716ff0a62f80356b5e61555d0d"

[[package]]
name = "siphasher"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56199f7ddabf13fe5074ce809e7d3f42b42ae711800501b5b16ea82ad029c39d"

[[package]]
name = "sized-chunks"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d69225bde7a69b235da73377861095455d298f2b970996eec25ddbb42b3d1e"
dependencies = [
 "bitmaps",
 "typenum",
]

[[package]]
name = "sketches-ddsketch"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85636c14b73d81f541e525f585c0a2109e6744e1565b5c1668e31c70c10ed65c"
dependencies = [
 "serde",
]

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "sled"
version = "0.34.6"
source = "git+https://github.com/datafuse-extras/sled?tag=v0.34.7-datafuse.1#43fa7250d3c6f4964167c9498b622f2923289cf3"
dependencies = [
 "crc32fast",
 "fs2",
 "im",
 "libc",
 "log",
 "parking_lot 0.11.2",
 "rio",
]

[[package]]
name = "slice-group-by"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "826167069c09b99d56f31e9ae5c99049e932a98c9dc2dac47645b08dbbf76ba7"

[[package]]
name = "small_ctor"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88414a5ca1f85d82cc34471e975f0f74f6aa54c40f062efa42c0080e7f763f81"

[[package]]
name = "smallvec"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8917285742e9f3e1683f0a9c4e6b57960b7314d0b08d30d1ecd426713ee2eee9"
dependencies = [
 "serde",
]

[[package]]
name = "snafu"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "223891c85e2a29c3fe8fb900c1fae5e69c2e42415e3177752e8718475efa5019"
dependencies = [
 "snafu-derive",
]

[[package]]
name = "snafu-derive"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03c3c6b7927ffe7ecaa769ee0e3994da3b8cafc8f444578982c83ecb161af917"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "snailquote"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec62a949bda7f15800481a711909f946e1204f2460f89210eaf7f57730f88f86"
dependencies = [
 "thiserror 1.0.69",
 "unicode_categories",
]

[[package]]
name = "snap"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b6b67fb9a61334225b5b790716f609cd58395f895b3fe8b328786812a40bc3b"

[[package]]
name = "socket2"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4f5fd57c80058a56cf5c777ab8a126398ece8e442983605d280a44ce79d0edef"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "sonic-number"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8a74044c092f4f43ca7a6cfd62854cf9fb5ac8502b131347c990bf22bef1dfe"
dependencies = [
 "cfg-if",
]

[[package]]
name = "sonic-rs"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0275f9f2f07d47556fe60c2759da8bc4be6083b047b491b2d476aa0bfa558eb1"
dependencies = [
 "bumpalo",
 "bytes",
 "cfg-if",
 "faststr",
 "itoa",
 "ref-cast",
 "ryu",
 "serde",
 "simdutf8",
 "sonic-number",
 "sonic-simd",
 "thiserror 2.0.12",
]

[[package]]
name = "sonic-simd"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b421f7b6aa4a5de8f685aaf398dfaa828346ee639d2b1c1061ab43d40baa6223"
dependencies = [
 "cfg-if",
]

[[package]]
name = "spade"
version = "2.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ece03ff43cd2a9b57ebf776ea5e78bd30b3b4185a619f041079f4109f385034"
dependencies = [
 "hashbrown 0.15.3",
 "num-traits",
 "robust",
 "smallvec",
]

[[package]]
name = "span-map"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9374d7f3fc7a34774575f8ebf4f55d7c19ccd09e77e7bd40d33064fe728e3926"

[[package]]
name = "spdx"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58b69356da67e2fc1f542c71ea7e654a361a79c938e4424392ecf4fa065d2193"
dependencies = [
 "smallvec",
]

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"
dependencies = [
 "lock_api",
]

[[package]]
name = "spin"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d5fe4ccb98d9c292d56fec89a5e07da7fc4cf0dc11e156b41793132775d3e591"
dependencies = [
 "lock_api",
]

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der",
]

[[package]]
name = "sptr"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b9b39299b249ad65f3b7e96443bad61c02ca5cd3589f46cb6d610a0fd6c0d6a"

[[package]]
name = "sqlformat"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7bba3a93db0cc4f7bdece8bb09e77e2e785c20bfebf79eb8340ed80708048790"
dependencies = [
 "nom 7.1.3",
 "unicode_categories",
]

[[package]]
name = "sqllogictest"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94181af64007792bd1ab6d22023fbe86c2ccc50c1031b5bac554b5d057597e7b"
dependencies = [
 "async-trait",
 "educe",
 "fs-err",
 "futures",
 "glob",
 "humantime",
 "itertools 0.13.0",
 "libtest-mimic",
 "md-5",
 "owo-colors",
 "rand 0.8.5",
 "regex",
 "similar",
 "subst",
 "tempfile",
 "thiserror 2.0.12",
 "tracing",
]

[[package]]
name = "sqlparser"
version = "0.43.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f95c4bae5aba7cd30bd506f7140026ade63cff5afd778af8854026f9606bf5d4"
dependencies = [
 "log",
 "serde",
]

[[package]]
name = "sqlparser"
version = "0.56.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e68feb51ffa54fc841e086f58da543facfe3d7ae2a60d69b0a8cbbd30d16ae8d"
dependencies = [
 "log",
 "recursive",
]

[[package]]
name = "sqlx"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3c3a85280daca669cfd3bcb68a337882a8bc57ec882f72c5d13a430613a738e"
dependencies = [
 "sqlx-core",
 "sqlx-macros",
 "sqlx-mysql",
 "sqlx-postgres",
 "sqlx-sqlite",
]

[[package]]
name = "sqlx-core"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f743f2a3cea30a58cd479013f75550e879009e3a02f616f18ca699335aa248c3"
dependencies = [
 "base64 0.22.1",
 "bytes",
 "crc",
 "crossbeam-queue",
 "either",
 "event-listener 5.4.0",
 "futures-core",
 "futures-intrusive",
 "futures-io",
 "futures-util",
 "hashbrown 0.15.3",
 "hashlink 0.10.0",
 "indexmap 2.9.0",
 "log",
 "memchr",
 "once_cell",
 "percent-encoding",
 "serde",
 "serde_json",
 "sha2",
 "smallvec",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tracing",
 "url",
]

[[package]]
name = "sqlx-macros"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f4200e0fde19834956d4252347c12a083bdcb237d7a1a1446bffd8768417dce"
dependencies = [
 "proc-macro2",
 "quote",
 "sqlx-core",
 "sqlx-macros-core",
 "syn 2.0.101",
]

[[package]]
name = "sqlx-macros-core"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "882ceaa29cade31beca7129b6beeb05737f44f82dbe2a9806ecea5a7093d00b7"
dependencies = [
 "dotenvy",
 "either",
 "heck 0.5.0",
 "hex",
 "once_cell",
 "proc-macro2",
 "quote",
 "serde",
 "serde_json",
 "sha2",
 "sqlx-core",
 "sqlx-mysql",
 "sqlx-postgres",
 "sqlx-sqlite",
 "syn 2.0.101",
 "tempfile",
 "tokio",
 "url",
]

[[package]]
name = "sqlx-mysql"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0afdd3aa7a629683c2d750c2df343025545087081ab5942593a5288855b1b7a7"
dependencies = [
 "atoi",
 "base64 0.22.1",
 "bitflags 2.9.0",
 "byteorder",
 "bytes",
 "crc",
 "digest",
 "dotenvy",
 "either",
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-util",
 "generic-array",
 "hex",
 "hkdf",
 "hmac",
 "itoa",
 "log",
 "md-5",
 "memchr",
 "once_cell",
 "percent-encoding",
 "rand 0.8.5",
 "rsa",
 "serde",
 "sha1",
 "sha2",
 "smallvec",
 "sqlx-core",
 "stringprep",
 "thiserror 2.0.12",
 "tracing",
 "whoami",
]

[[package]]
name = "sqlx-postgres"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0bedbe1bbb5e2615ef347a5e9d8cd7680fb63e77d9dafc0f29be15e53f1ebe6"
dependencies = [
 "atoi",
 "base64 0.22.1",
 "bitflags 2.9.0",
 "byteorder",
 "crc",
 "dotenvy",
 "etcetera",
 "futures-channel",
 "futures-core",
 "futures-util",
 "hex",
 "hkdf",
 "hmac",
 "home",
 "itoa",
 "log",
 "md-5",
 "memchr",
 "once_cell",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "sha2",
 "smallvec",
 "sqlx-core",
 "stringprep",
 "thiserror 2.0.12",
 "tracing",
 "whoami",
]

[[package]]
name = "sqlx-sqlite"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c26083e9a520e8eb87a06b12347679b142dc2ea29e6e409f805644a7a979a5bc"
dependencies = [
 "atoi",
 "flume",
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-intrusive",
 "futures-util",
 "libsqlite3-sys",
 "log",
 "percent-encoding",
 "serde",
 "serde_urlencoded",
 "sqlx-core",
 "thiserror 2.0.12",
 "tracing",
 "url",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "stacker"
version = "0.1.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cddb07e32ddb770749da91081d8d0ac3a16f1a569a18b20348cd371f5dead06b"
dependencies = [
 "cc",
 "cfg-if",
 "libc",
 "psm",
 "windows-sys 0.59.0",
]

[[package]]
name = "state"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b8c4a4445d81357df8b1a650d0d0d6fbbbfe99d064aa5e02f3e4022061476d8"
dependencies = [
 "loom 0.5.6",
]

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "str_stack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091b6114800a5f2141aee1d1b9d6ca3592ac062dc5decb3764ec5895a47b4eb"

[[package]]
name = "stream-more"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21653f80abaae0db734237d6fc78eb5673219b30ddc2df8f32aaf15d939bec58"
dependencies = [
 "binary-heap-plus",
 "compare",
 "futures",
 "pin-project-lite",
]

[[package]]
name = "strength_reduce"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe895eb47f22e2ddd4dabc02bce419d2e643c8e3b585c78158b349195bc24d82"

[[package]]
name = "stringprep"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b4df3d392d81bd458a8a621b8bffbd2302a12ffe288a9d931670948749463b1"
dependencies = [
 "unicode-bidi",
 "unicode-normalization",
 "unicode-properties",
]

[[package]]
name = "stringslice"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8dad614e9c8a0773603982e0c1d3192f2bce1c0ee193caf0526b8ae3c6c3e38c"

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "strsim"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7da8b5736845d9f2fcb837ea5d9e2628564b3b043a70948a3f0b778838c5fb4f"

[[package]]
name = "structmeta"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e1575d8d40908d70f6fd05537266b90ae71b15dbbe7a8b7dffa2b759306d329"
dependencies = [
 "proc-macro2",
 "quote",
 "structmeta-derive",
 "syn 2.0.101",
]

[[package]]
name = "structmeta-derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "152a0b65a590ff6c3da95cabe2353ee04e6167c896b28e3b14478c2636c922fc"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"

[[package]]
name = "strum"
version = "0.26.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fec0f0aef304996cf250b31b5a10dee7980c85da9d759361292b8bca5a18f06"
dependencies = [
 "strum_macros 0.26.4",
]

[[package]]
name = "strum"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f64def088c51c9510a8579e3c5d67c65349dcf755e5479ad3d010aa6454e2c32"
dependencies = [
 "strum_macros 0.27.1",
]

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.1",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c6bee85a5a24955dc440386795aa378cd9cf82acd5f764469152d2270e581be"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "strum_macros"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c77a8c5abcaf0f9ce05d62342b7d298c346515365c36b673df4ebe3ced01fde8"
dependencies = [
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "rustversion",
 "syn 2.0.101",
]

[[package]]
name = "sub-cache"
version = "0.2.1"
source = "git+https://github.com/databendlabs/sub-cache?tag=v0.2.1#2826eb3290f6f9830b469d3808aefdca444f893f"
dependencies = [
 "async-trait",
 "futures",
 "log",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "subprocess"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2e86926081dda636c546d8c5e641661049d7562a68f5488be4a1f7f66f6086"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "subst"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a9a86e5144f63c2d18334698269a8bfae6eece345c70b64821ea5b35054ec99"
dependencies = [
 "memchr",
 "unicode-width 0.1.14",
]

[[package]]
name = "subtle"
version = "2.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13c2bddecc57b384dee18652358fb23172facb8a2c51ccc10d74c157bdea3292"

[[package]]
name = "superboring"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "515cce34a781d7250b8a65706e0f2a5b99236ea605cb235d4baed6685820478f"
dependencies = [
 "getrandom 0.2.16",
 "hmac-sha256",
 "hmac-sha512",
 "rand 0.8.5",
 "rsa",
]

[[package]]
name = "sval"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cc9739f56c5d0c44a5ed45473ec868af02eb896af8c05f616673a31e1d1bb09"

[[package]]
name = "sval_buffer"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f39b07436a8c271b34dad5070c634d1d3d76d6776e938ee97b4a66a5e8003d0b"
dependencies = [
 "sval",
 "sval_ref",
]

[[package]]
name = "sval_dynamic"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffcb072d857431bf885580dacecf05ed987bac931230736739a79051dbf3499b"
dependencies = [
 "sval",
]

[[package]]
name = "sval_fmt"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f214f427ad94a553e5ca5514c95c6be84667cbc5568cce957f03f3477d03d5c"
dependencies = [
 "itoa",
 "ryu",
 "sval",
]

[[package]]
name = "sval_json"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "389ed34b32e638dec9a99c8ac92d0aa1220d40041026b625474c2b6a4d6f4feb"
dependencies = [
 "itoa",
 "ryu",
 "sval",
]

[[package]]
name = "sval_nested"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14bae8fcb2f24fee2c42c1f19037707f7c9a29a0cda936d2188d48a961c4bb2a"
dependencies = [
 "sval",
 "sval_buffer",
 "sval_ref",
]

[[package]]
name = "sval_ref"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a4eaea3821d3046dcba81d4b8489421da42961889902342691fb7eab491d79e"
dependencies = [
 "sval",
]

[[package]]
name = "sval_serde"
version = "2.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "172dd4aa8cb3b45c8ac8f3b4111d644cd26938b0643ede8f93070812b87fb339"
dependencies = [
 "serde",
 "sval",
 "sval_nested",
]

[[package]]
name = "symbolic-common"
version = "12.15.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a1150bdda9314f6cfeeea801c23f5593c6e6a6c72e64f67e48d723a12b8efdb"
dependencies = [
 "debugid",
 "memmap2",
 "stable_deref_trait",
 "uuid",
]

[[package]]
name = "symbolic-demangle"
version = "12.15.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f66537def48fbc704a92e4fdaab7833bc7cb2255faca8182592fb5fa617eb82"
dependencies = [
 "cpp_demangle",
 "rustc-demangle",
 "symbolic-common",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.101"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ce2b7fc941b3a24138a0a7cf8e858bfc6a992e7978a068a5c760deb0ed43caf"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "syn-mid"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea305d57546cc8cd04feb14b62ec84bf17f50e3f7b12560d7bfa9265f39d9ed"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 1.0.109",
]

[[package]]
name = "sync_wrapper"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf256ce5efdfa370213c1dabab5935a12e49f2c58d15e9eac2870d3b4f27263"
dependencies = [
 "futures-core",
]

[[package]]
name = "synstructure"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "728a70f3dbaf5bab7f0c4b1ac8d7ae5ea60a4b5549c8a5914361c99147a709d2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "sys-info"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b3a0d0aba8bf96a0e1ddfdc352fc53b3df7f39318c71854910c3c4b024ae52c"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "sysinfo"
version = "0.34.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4b93974b3d3aeaa036504b8eefd4c039dced109171c1ae973f1dc63b2c7e4b2"
dependencies = [
 "libc",
 "memchr",
 "ntapi",
 "objc2-core-foundation",
 "windows 0.57.0",
]

[[package]]
name = "system-configuration"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c879d448e9d986b661742763247d3693ed13609438cf3d006f51f5368a5ba6b"
dependencies = [
 "bitflags 2.9.0",
 "core-foundation 0.9.4",
 "system-configuration-sys",
]

[[package]]
name = "system-configuration-sys"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e1d1b10ced5ca923a1fcb8d03e96b8d3268065d724548c0211415ff6ac6bac4"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "system-interface"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc4592f674ce18521c2a81483873a49596655b179f71c5e05d10c1fe66c78745"
dependencies = [
 "bitflags 2.9.0",
 "cap-fs-ext",
 "cap-std",
 "fd-lock",
 "io-lifetimes",
 "rustix 0.38.44",
 "windows-sys 0.59.0",
 "winx",
]

[[package]]
name = "tagptr"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b2093cf4c8eb1e67749a6762251bc9cd836b6fc171623bd0a9d324d37af2417"

[[package]]
name = "tantivy"
version = "0.22.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "aho-corasick",
 "arc-swap",
 "base64 0.22.1",
 "bitpacking 0.9.2",
 "byteorder",
 "census",
 "crc32fast",
 "crossbeam-channel",
 "downcast-rs",
 "fastdivide",
 "fnv",
 "fs4",
 "htmlescape",
 "itertools 0.12.1",
 "levenshtein_automata",
 "log",
 "lru",
 "lz4_flex",
 "measure_time",
 "memmap2",
 "num_cpus",
 "once_cell",
 "oneshot",
 "rayon",
 "regex",
 "rust-stemmers",
 "rustc-hash 1.1.0",
 "serde",
 "serde_json",
 "sketches-ddsketch",
 "smallvec",
 "tantivy-bitpacker",
 "tantivy-columnar",
 "tantivy-common",
 "tantivy-fst",
 "tantivy-query-grammar",
 "tantivy-stacker",
 "tantivy-tokenizer-api",
 "tempfile",
 "thiserror 1.0.69",
 "time",
 "uuid",
 "winapi",
]

[[package]]
name = "tantivy-bitpacker"
version = "0.6.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "bitpacking 0.9.2",
]

[[package]]
name = "tantivy-columnar"
version = "0.3.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "downcast-rs",
 "fastdivide",
 "itertools 0.12.1",
 "serde",
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-sstable",
 "tantivy-stacker",
]

[[package]]
name = "tantivy-common"
version = "0.7.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "async-trait",
 "byteorder",
 "ownedbytes",
 "serde",
 "time",
]

[[package]]
name = "tantivy-fst"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d60769b80ad7953d8a7b2c70cdfe722bbcdcac6bccc8ac934c40c034d866fc18"
dependencies = [
 "byteorder",
 "regex-syntax 0.8.5",
 "utf8-ranges",
]

[[package]]
name = "tantivy-jieba"
version = "0.11.0"
source = "git+https://github.com/datafuse-extras/tantivy-jieba?rev=0e300e9#0e300e9085651b7e6659dfcc7b0ea0fa9cab09c2"
dependencies = [
 "jieba-rs",
 "lazy_static",
 "tantivy-tokenizer-api",
]

[[package]]
name = "tantivy-query-grammar"
version = "0.22.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "tantivy-sstable"
version = "0.3.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "tantivy-bitpacker",
 "tantivy-common",
 "tantivy-fst",
 "zstd 0.13.3",
]

[[package]]
name = "tantivy-stacker"
version = "0.3.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "murmurhash32",
 "rand_distr",
 "tantivy-common",
]

[[package]]
name = "tantivy-tokenizer-api"
version = "0.3.0"
source = "git+https://github.com/datafuse-extras/tantivy?rev=7502370#7502370b68e6822a687ee071660e350b67808533"
dependencies = [
 "serde",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61c41af27dd6d1e27b1b16b489db798443478cef1f06a660c96db617ba5de3b1"

[[package]]
name = "target-lexicon"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e502f78cdbb8ba4718f566c418c52bc729126ffd16baee5baa718cf25dd5a69a"

[[package]]
name = "temp-env"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96374855068f47402c3121c6eed88d29cb1de8f3ab27090e273e420bdabcf050"
dependencies = [
 "parking_lot 0.12.3",
]

[[package]]
name = "tempfile"
version = "3.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a64e3985349f2441a1a9ef0b853f869006c3855f2cda6862a94d26ebb9d6a1"
dependencies = [
 "fastrand",
 "getrandom 0.3.3",
 "once_cell",
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "termcolor"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06794f8f6c5c898b3275aebefa6b8a1cb24cd2c6c79397ab15774837a0bc5755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "terminal_size"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45c6481c4829e4cc63825e62c49186a34538b7b2750b73b266581ffb612fb5ed"
dependencies = [
 "rustix 1.0.7",
 "windows-sys 0.59.0",
]

[[package]]
name = "termtree"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f50febec83f5ee1df3015341d8bd429f2d1cc62bcba7ea2076759d315084683"

[[package]]
name = "test-harness"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae861f7d521762a2e5524ceeb3a518fab2c06c25e217a1d7270b8c5e158c141b"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "testcontainers"
version = "0.23.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f40cc2bd72e17f328faf8ca7687fe337e61bccd8acf9674fa78dd3792b045e1"
dependencies = [
 "async-trait",
 "bollard",
 "bollard-stubs",
 "bytes",
 "docker_credential",
 "either",
 "etcetera",
 "futures",
 "log",
 "memchr",
 "parse-display",
 "pin-project-lite",
 "serde",
 "serde_json",
 "serde_with",
 "thiserror 1.0.69",
 "tokio",
 "tokio-stream",
 "tokio-tar",
 "tokio-util",
 "url",
]

[[package]]
name = "testcontainers-modules"
version = "0.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d43ed4e8f58424c3a2c6c56dbea6643c3c23e8666a34df13c54f0a184e6c707"
dependencies = [
 "testcontainers",
]

[[package]]
name = "thiserror"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6aaf5339b578ea85b50e080feb250a3e8ae8cfcdff9a461c9ec2904bc923f52"
dependencies = [
 "thiserror-impl 1.0.69",
]

[[package]]
name = "thiserror"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "567b8a2dae586314f7be2a752ec7474332959c6460e02bde30d702a66d488708"
dependencies = [
 "thiserror-impl 2.0.12",
]

[[package]]
name = "thiserror-impl"
version = "1.0.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fee6c4efc90059e10f81e6d42c60a18f76588c3d74cb83a0b242a2b6c7504c1"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thiserror-impl"
version = "2.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f7cf42b4507d8ea322120659672cf1b9dbb93f8f2d4ecfd6e51350ff5b17a1d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if",
 "once_cell",
]

[[package]]
name = "threadpool"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d050e60b33d41c19108b32cea32164033a9013fe3b46cbd4457559bfbf77afaa"
dependencies = [
 "num_cpus",
]

[[package]]
name = "thrift"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e54bc85fc7faa8bc175c4bab5b92ba8d9a3ce893d0e9f42cc455c8ab16a9e09"
dependencies = [
 "byteorder",
 "integer-encoding 3.0.4",
 "log",
 "ordered-float 2.10.1",
 "threadpool",
]

[[package]]
name = "tikv-jemalloc-ctl"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f21f216790c8df74ce3ab25b534e0718da5a1916719771d3fec23315c99e468b"
dependencies = [
 "libc",
 "paste",
 "tikv-jemalloc-sys",
]

[[package]]
name = "tikv-jemalloc-sys"
version = "0.6.0****.0-1-ge13ca993e8ccb9ba9847cc330696e02839f328f7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd3c60906412afa9c2b5b5a48ca6a5abe5736aec9eb48ad05037a677e52e4e2d"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "time"
version = "0.3.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a7619e19bc266e0f9c5e6686659d394bc57973859340060a69221e57dbc0c40"
dependencies = [
 "deranged",
 "itoa",
 "libc",
 "num-conv",
 "num_threads",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9e9a38711f559d9e3ce1cdb06dd7c5b8ea546bc90052da6d06bb76da74bb07c"

[[package]]
name = "time-macros"
version = "0.2.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3526739392ec93fd8b359c8e98514cb3e8e021beb4e5f597b00a0221f8ed8a49"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinystr"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d4f6d1145dcb577acf783d4e601bc1d76a13337bb54e6233add580b07344c8b"
dependencies = [
 "displaydoc",
 "zerovec",
]

[[package]]
name = "tinyvec"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09b3661f17e86524eccd4371ab0429194e0d7c008abb45f7a7495b1719463c71"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "tokio"
version = "1.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2513ca694ef9ede0fb23fe71a4ee4107cb102b9dc1930f6d0fd77aae068ae165"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio 1.0.3",
 "parking_lot 0.12.3",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2",
 "tokio-macros",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-macros"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e06d43f1345a3bcd39f6a56dbb7dcab2ba47e68e8ac134855e7e2bdbaf8cab8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbae76ab933c85776efabc971569dd6119c580d8f5d448769dec1764bf796ef2"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-openssl"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59df6849caa43bb7567f9a36f863c447d95a11d5903c9cc334ba32576a27eadd"
dependencies = [
 "openssl",
 "openssl-sys",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.12",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e727b36a1a0e8b74c376ac2211e40c2c8af09fb4013c60d910495810f008e9b"
dependencies = [
 "rustls 0.23.27",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eca58d7bba4a75707817a2c44174253f9236b2d5fbd055602e9d5c07c139a047"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-tar"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d5714c010ca3e5c27114c1cdeb9d14641ace49874aa5626d7149e47aedace75"
dependencies = [
 "filetime",
 "futures-core",
 "libc",
 "redox_syscall 0.3.5",
 "tokio",
 "tokio-stream",
 "xattr",
]

[[package]]
name = "tokio-util"
version = "0.7.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66a539a9ad6d5d281510d5bd368c973d636c02dbf8a67300bfb6b950696ad7df"
dependencies = [
 "bytes",
 "futures-core",
 "futures-sink",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "toml"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd79e69d3b627db300ff956027cc6c3798cef26d22526befdfcd12feeb6d2257"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit 0.19.15",
]

[[package]]
name = "toml"
version = "0.8.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05ae329d1f08c4d17a59bed7ff5b5a769d062e64a62d34a3261b219e62cd5aae"
dependencies = [
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_edit 0.22.26",
]

[[package]]
name = "toml_datetime"
version = "0.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3da5db5a963e24bc68be8b17b6fa82814bb22ee8660f192bb182771d498f09a3"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.19.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b5bb770da30e5cbfde35a2d7b9b8a2c4b8ef89548a7a6aeab5c9a576e3e7421"
dependencies = [
 "indexmap 2.9.0",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.22.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "310068873db2c5b3e7659d2cc35d21855dbafa50d1ce336397c666e3cb08137e"
dependencies = [
 "indexmap 2.9.0",
 "serde",
 "serde_spanned",
 "toml_datetime",
 "toml_write",
 "winnow 0.7.10",
]

[[package]]
name = "toml_write"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfb942dfe1d8e29a7ee7fcbde5bd2b9a25fb89aa70caea2eba3bee836ff41076"

[[package]]
name = "tonic"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "877c5b330756d856ffcc4553ab34a5684481ade925ecc54bcd1bf02b1d0d4d52"
dependencies = [
 "async-stream",
 "async-trait",
 "axum",
 "base64 0.22.1",
 "bytes",
 "h2 0.4.10",
 "http 1.3.1",
 "http-body 1.0.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-timeout",
 "hyper-util",
 "percent-encoding",
 "pin-project",
 "prost",
 "rustls-native-certs 0.8.1",
 "rustls-pemfile 2.2.0",
 "socket2",
 "tokio",
 "tokio-rustls 0.26.2",
 "tokio-stream",
 "tower 0.4.13",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tonic-build"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9557ce109ea773b399c9b9e5dca39294110b74f1f342cb347a80d1fce8c26a11"
dependencies = [
 "prettyplease",
 "proc-macro2",
 "prost-build",
 "prost-types",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tonic-reflection"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "878d81f52e7fcfd80026b7fdb6a9b578b3c3653ba987f87f0dce4b64043cba27"
dependencies = [
 "prost",
 "prost-types",
 "tokio",
 "tokio-stream",
 "tonic",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "indexmap 1.9.3",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d039ad9159c98b70ecfd540b2573b97f7f52c3e8d9f8ad57a24b916a536975f9"
dependencies = [
 "futures-core",
 "futures-util",
 "pin-project-lite",
 "sync_wrapper",
 "tokio",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "tower-layer"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "121c2a6cda46980bb0fcd1647ffaf6cd3fc79a013de288782836f6df9c48780e"

[[package]]
name = "tower-service"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8df9b6e13f2d32c91b9bd719c00d1958837bc7dec474d94952798cc8e69eeec3"

[[package]]
name = "tracing"
version = "0.1.41"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "784e0ac535deb450455cbfa28a6f0df145ea1bb7ae51b821cf5e7927fdcfbdd0"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-attributes"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "395ae124c09f9e6918a2310af6038fba074bcf474ac352496d5910dd59a2226d"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "tracing-core"
version = "0.1.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e672c95779cf947c5311f83787af4fa8fffd12fb27e4993211a84bdfd9610f9c"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-error"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b1581020d7a273442f5b45074a6a57d5757ad0a47dac0e9f0bd57b81936f3db"
dependencies = [
 "tracing",
 "tracing-subscriber",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-serde"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "704b1aeb7be0d0a84fc9828cae51dab5970fee5088f83d1dd7ee6f6246fc6ff1"
dependencies = [
 "serde",
 "tracing-core",
 "valuable",
 "valuable-serde",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8189decb5ac0fa7bc8b96b7cb9b2701d60d48805aca84a238004d665fcc4008"
dependencies = [
 "matchers",
 "nu-ansi-term",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "tracing",
 "tracing-core",
 "tracing-log",
 "tracing-serde",
 "valuable",
 "valuable-serde",
]

[[package]]
name = "trim-in-place"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "343e926fc669bc8cde4fa3129ab681c63671bae288b1f1081ceee6d9d37904fc"

[[package]]
name = "triple_accel"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "622b09ce2fe2df4618636fb92176d205662f59803f39e70d1c333393082de96c"

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "twox-hash"
version = "1.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fee6b57c6a41524a810daee9286c02d7752c4253064d0b05472833a438f675"
dependencies = [
 "cfg-if",
 "rand 0.8.5",
 "static_assertions",
]

[[package]]
name = "twox-hash"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7b17f197b3050ba473acf9181f7b1d3b66d1cf7356c6cc57886662276e65908"

[[package]]
name = "typed-builder"
version = "0.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06fbd5b8de54c5f7c91f6fe4cebb949be2125d7758e630bb58b1d831dbce600"
dependencies = [
 "typed-builder-macro 0.19.1",
]

[[package]]
name = "typed-builder"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd9d30e3a08026c78f246b173243cf07b3696d274debd26680773b6773c2afc7"
dependencies = [
 "typed-builder-macro 0.20.1",
]

[[package]]
name = "typed-builder-macro"
version = "0.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9534daa9fd3ed0bd911d462a37f172228077e7abf18c18a5f67199d959205f8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "typed-builder-macro"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c36781cc0e46a83726d9879608e4cf6c2505237e263a8eb8c24502989cfdb28"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "typeid"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc7d623258602320d5c55d1bc22793b57daff0ec7efc270ea7d55ce1d5f5471c"

[[package]]
name = "typenum"
version = "1.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1dccffe3ce07af9386bfd29e80c0ab1a8205a2fc34e4bcd40364df902cfa8f3f"

[[package]]
name = "typetag"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73f22b40dd7bfe8c14230cf9702081366421890435b2d625fa92b4acc4c3de6f"
dependencies = [
 "erased-serde",
 "inventory",
 "once_cell",
 "serde",
 "typetag-impl",
]

[[package]]
name = "typetag-impl"
version = "0.2.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35f5380909ffc31b4de4f4bdf96b877175a016aa2ca98cee39fcfd8c4d53d952"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "typewit"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb77c29baba9e4d3a6182d51fa75e3215c7fd1dab8f4ea9d107c716878e55fc0"
dependencies = [
 "typewit_proc_macros",
]

[[package]]
name = "typewit_proc_macros"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e36a83ea2b3c704935a01b4642946aadd445cea40b10935e3f8bd8052b8193d6"

[[package]]
name = "uluru"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c8a2469e56e6e5095c82ccd3afb98dad95f7af7929aab6d8ba8d6e0f73657da"
dependencies = [
 "arrayvec",
]

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "uncased"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1b88fcfe09e89d3866a5c11019378088af2d24c3fbd4f0543f96b479ec90697"
dependencies = [
 "version_check",
]

[[package]]
name = "unicase"
version = "2.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75b844d17643ee918803943289730bec8aac480150456169e647ed0b576ba539"

[[package]]
name = "unicode-bidi"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c1cb5db39152898a79168971543b1cb5020dff7fe43c8dc468b0885f5e29df5"

[[package]]
name = "unicode-bom"
version = "2.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7eec5d1121208364f6793f7d2e222bf75a915c19557537745b195b253dd64217"

[[package]]
name = "unicode-ident"
version = "1.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a5f39404a5da50712a4c1eecf25e90dd62b613502b7e925fd4e4d19b5c96512"

[[package]]
name = "unicode-normalization"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5033c97c4262335cded6d6fc3e5c18ab755e1a3dc96376350f3d8e9f009ad956"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-properties"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e70f2a8b45122e719eb623c01822704c4e0907e7e426a05927e1a1cfff5b75d0"

[[package]]
name = "unicode-segmentation"
version = "1.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6ccf251212114b54433ec949fd6a7841275f9ada20dddd2f29e9ceea4501493"

[[package]]
name = "unicode-width"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd6e30e90baa6f72411720665d41d89b9a3d039dc45b8faea1ddd07f617f6af"

[[package]]
name = "unicode-width"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fc81956842c57dac11422a97c3b8195a1ff727f06e85c84ed2e8aa277c9a0fd"

[[package]]
name = "unicode-xid"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc1c04c71510c7f702b52b7c350734c9ff1295c464a03335b00bb84fc54f853"

[[package]]
name = "unicode_categories"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "39ec24b3121d976906ece63c9daad25b85969647682eee313cb5779fdd69e14e"

[[package]]
name = "unindent"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7264e107f553ccae879d21fbea1d6724ac785e8c3bfc762137959b5802826ef3"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "unsafe-libyaml"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673aac59facbab8a9007c7f6108d11f63b603f7cabff99fabf650fea5c32b861"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "unty"
version = "0.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d49784317cd0d1ee7ec5c716dd598ec5b4483ea832a2dced265471cc0f690ae"

[[package]]
name = "ureq"
version = "2.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02d1a66277ed75f640d608235660df48c8e3c19f3b4edb6a263315626cc3c01d"
dependencies = [
 "base64 0.22.1",
 "flate2",
 "log",
 "once_cell",
 "rustls 0.23.27",
 "rustls-pki-types",
 "serde",
 "serde_json",
 "url",
 "webpki-roots 0.26.11",
]

[[package]]
name = "url"
version = "2.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32f8b686cadd1473f4bd0117a5d28d36b1ade384ea9b5069a1c40aefed7fda60"
dependencies = [
 "form_urlencoded",
 "idna",
 "percent-encoding",
 "serde",
]

[[package]]
name = "urlencoding"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daf8dba3b7eb870caf1ddeed7bc9d2a049f3cfdfae7cb521b087cc33ae4c49da"

[[package]]
name = "utf8-ranges"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fcfc827f90e53a02eaef5e535ee14266c1d569214c6aa70133a624d8a3164ba"

[[package]]
name = "utf8-width"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86bd8d4e895da8537e5315b8254664e6b769c4ff3db18321b297a1e7004392e3"

[[package]]
name = "utf8_iter"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c140620e7ffbb22c2dee59cafe6084a59b5ffc27a8859a5f0d494b5d52b6be"

[[package]]
name = "utf8parse"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06abde3611657adf66d383f00b093d7faecc7fa57071cce2578660c9f1010821"

[[package]]
name = "uuid"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "458f7a779bf54acc9f347480ac654f68407d3aab21269a6e3c9f922acd9e2da9"
dependencies = [
 "getrandom 0.3.3",
 "js-sys",
 "rand 0.9.1",
 "serde",
 "wasm-bindgen",
]

[[package]]
name = "validit"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1fad49f3eae9c160c06b4d49700a99e75817f127cf856e494b56d5e23170020"
dependencies = [
 "anyerror",
]

[[package]]
name = "valuable"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba73ea9cf16a25df0c8caa16c51acb937d5712a8429db78a3ee29d5dcacd3a65"

[[package]]
name = "valuable-serde"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ee0548edecd1b907be7e67789923b7d02275b9ba4a33ebc33300e2c947a8cb1"
dependencies = [
 "serde",
 "valuable",
]

[[package]]
name = "value-bag"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "943ce29a8a743eb10d6082545d861b24f9d1b160b7d741e0f2cdf726bec909c5"
dependencies = [
 "value-bag-serde1",
 "value-bag-sval2",
]

[[package]]
name = "value-bag-serde1"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35540706617d373b118d550d41f5dfe0b78a0c195dc13c6815e92e2638432306"
dependencies = [
 "erased-serde",
 "serde",
 "serde_fmt",
]

[[package]]
name = "value-bag-sval2"
version = "1.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe7e140a2658cc16f7ee7a86e413e803fc8f9b5127adc8755c19f9fefa63a52"
dependencies = [
 "sval",
 "sval_buffer",
 "sval_dynamic",
 "sval_fmt",
 "sval_json",
 "sval_ref",
 "sval_serde",
]

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "vergen"
version = "8.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2990d9ea5967266ea0ccf413a4aa5c42a93dbcfda9cb49a97de6931726b12566"
dependencies = [
 "anyhow",
 "cargo_metadata",
 "cfg-if",
 "gix",
 "regex",
 "rustc_version",
 "rustversion",
 "time",
]

[[package]]
name = "version_check"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b928f33d975fc6ad9f86c8f283853ad26bdd5b10b7f1542aa2fa15e2289105a"

[[package]]
name = "virtue"
version = "0.0.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "051eb1abcf10076295e815102942cc58f9d5e3b4560e46e53c21e8ff6f3af7b1"

[[package]]
name = "volo"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13832c5e356d13ae8944fa06ef9cf64ea24ea14c0a2e347c4ead078c927d4ade"
dependencies = [
 "async-broadcast",
 "dashmap 6.1.0",
 "faststr",
 "futures",
 "libc",
 "metainfo",
 "motore",
 "mur3",
 "nix 0.29.0",
 "once_cell",
 "pin-project",
 "rand 0.9.1",
 "socket2",
 "thiserror 2.0.12",
 "tokio",
 "tokio-stream",
 "tower 0.5.2",
 "tracing",
]

[[package]]
name = "volo-thrift"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4a41a936651c3b8206339898466fd4f39596cfedf789e5ee6a8d02a45feb45f"
dependencies = [
 "ahash 0.8.12",
 "anyhow",
 "bytes",
 "chrono",
 "futures",
 "itoa",
 "linked-hash-map",
 "linkedbytes",
 "metainfo",
 "motore",
 "num_enum",
 "parking_lot 0.12.3",
 "paste",
 "pilota",
 "pin-project",
 "rustc-hash 2.1.1",
 "scopeguard",
 "sonic-rs",
 "thiserror 2.0.12",
 "tokio",
 "tracing",
 "volo",
]

[[package]]
name = "vsimd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c3082ca00d5a5ef149bb8b555a72ae84c9c59f7250f013ac822ac2e49b19c64"

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasi"
version = "0.14.2+wasi-0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9683f9a5a998d873c0d21fcbe3c083009670149a8fab228644b8bd36b2c48cb3"
dependencies = [
 "wit-bindgen-rt",
]

[[package]]
name = "wasi-common"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "829f6c8c15912907b472bd9d195893bcdb1bde9cd8de55f134f6ab8aa507bf10"
dependencies = [
 "anyhow",
 "bitflags 2.9.0",
 "cap-fs-ext",
 "cap-rand",
 "cap-std",
 "cap-time-ext",
 "fs-set-times",
 "io-extras",
 "io-lifetimes",
 "log",
 "rustix 0.38.44",
 "system-interface",
 "thiserror 1.0.69",
 "tracing",
 "wasmtime",
 "wiggle",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasite"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8dad83b4f25e74f184f64c43b150b91efe7647395b42289f38e50566d82855b"

[[package]]
name = "wasix"
version = "0.12.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1fbb4ef9bbca0c1170e0b00dd28abc9e3b68669821600cad1caaed606583c6d"
dependencies = [
 "wasi 0.11.0+wasi-snapshot-preview1",
]

[[package]]
name = "wasm-bindgen"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1edc8929d7499fc4e8f0be2262a241556cfc54a0bea223790e71446f2aab1ef5"
dependencies = [
 "cfg-if",
 "once_cell",
 "rustversion",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f0a0651a5c2bc21487bde11ee802ccaf4c51935d0d3d42a6101f98161700bc6"
dependencies = [
 "bumpalo",
 "log",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "555d470ec0bc3bb57890405e5d4322cc9ea83cebb085523ced7be4144dac1e61"
dependencies = [
 "cfg-if",
 "js-sys",
 "once_cell",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7fe63fc6d09ed3792bd0897b314f53de8e16568c2b3f7982f468c0bf9bd0b407"
dependencies = [
 "quote",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ae87ea40c9f689fc23f209965b6fb8a99ad69aeeb0231408be24920604395de"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.100"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a05d73b933a847d6cccdda8f838a22ff101ad9bf93e33684f39c1f5f0eece3d"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "wasm-encoder"
version = "0.219.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8aa79bcd666a043b58f5fa62b221b0b914dd901e6f620e8ab7371057a797f3e1"
dependencies = [
 "leb128",
 "wasmparser 0.219.2",
]

[[package]]
name = "wasm-encoder"
version = "0.230.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4349d0943718e6e434b51b9639e876293093dca4b96384fb136ab5bd5ce6660"
dependencies = [
 "leb128fmt",
 "wasmparser 0.230.0",
]

[[package]]
name = "wasm-streams"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "15053d8d85c7eccdbefef60f06769760a563c7f0a9d6902a13d35c7800b0ad65"
dependencies = [
 "futures-util",
 "js-sys",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wasmparser"
version = "0.219.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5220ee4c6ffcc0cb9d7c47398052203bc902c8ef3985b0c8134118440c0b2921"
dependencies = [
 "ahash 0.8.12",
 "bitflags 2.9.0",
 "hashbrown 0.14.5",
 "indexmap 2.9.0",
 "semver",
 "serde",
]

[[package]]
name = "wasmparser"
version = "0.230.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "808198a69b5a0535583370a51d459baa14261dfab04800c4864ee9e1a14346ed"
dependencies = [
 "bitflags 2.9.0",
 "indexmap 2.9.0",
 "semver",
]

[[package]]
name = "wasmprinter"
version = "0.219.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c68c93bcc5e934985afd8b65214bdd77abd3863b2e1855eae1b07a11c4ef30a8"
dependencies = [
 "anyhow",
 "termcolor",
 "wasmparser 0.219.2",
]

[[package]]
name = "wasmtime"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b79302e3e084713249cc5622e8608e7410afdeeea8c8026d04f491d1fab0b4b"
dependencies = [
 "addr2line 0.24.2",
 "anyhow",
 "async-trait",
 "bitflags 2.9.0",
 "bumpalo",
 "cc",
 "cfg-if",
 "encoding_rs",
 "fxprof-processed-profile",
 "gimli 0.31.1",
 "hashbrown 0.14.5",
 "indexmap 2.9.0",
 "ittapi",
 "libc",
 "libm",
 "log",
 "mach2",
 "memfd",
 "object",
 "once_cell",
 "paste",
 "postcard",
 "psm",
 "pulley-interpreter",
 "rayon",
 "rustix 0.38.44",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "smallvec",
 "sptr",
 "target-lexicon 0.12.16",
 "wasm-encoder 0.219.2",
 "wasmparser 0.219.2",
 "wasmtime-asm-macros",
 "wasmtime-cache",
 "wasmtime-component-macro",
 "wasmtime-component-util",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "wasmtime-fiber",
 "wasmtime-jit-debug",
 "wasmtime-jit-icache-coherence",
 "wasmtime-slab",
 "wasmtime-versioned-export-macros",
 "wasmtime-winch",
 "wat",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-asm-macros"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe53a24e7016a5222875d8ca3ad6024b464465985693c42098cd0bb710002c28"
dependencies = [
 "cfg-if",
]

[[package]]
name = "wasmtime-cache"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0677a7e76c24746b68e3657f7cc50c0ff122ee7e97bbda6e710c1b790ebc93cb"
dependencies = [
 "anyhow",
 "base64 0.21.7",
 "directories-next",
 "log",
 "postcard",
 "rustix 0.38.44",
 "serde",
 "serde_derive",
 "sha2",
 "toml 0.8.22",
 "windows-sys 0.59.0",
 "zstd 0.13.3",
]

[[package]]
name = "wasmtime-component-macro"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e118acbd2bc09b32ad8606bc7cef793bf5019c1b107772e64dc6c76b5055d40b"
dependencies = [
 "anyhow",
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wasmtime-component-util",
 "wasmtime-wit-bindgen",
 "wit-parser",
]

[[package]]
name = "wasmtime-component-util"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a6db4f3ee18c699629eabb9c64e77efe5a93a5137f098db7cab295037ba41c2"

[[package]]
name = "wasmtime-cranelift"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b87e6c78f562b50aff1afd87ff32a57e241424c846c1c8f3c5fd352d2d62906"
dependencies = [
 "anyhow",
 "cfg-if",
 "cranelift-codegen",
 "cranelift-control",
 "cranelift-entity",
 "cranelift-frontend",
 "cranelift-native",
 "gimli 0.31.1",
 "itertools 0.12.1",
 "log",
 "object",
 "smallvec",
 "target-lexicon 0.12.16",
 "thiserror 1.0.69",
 "wasmparser 0.219.2",
 "wasmtime-environ",
 "wasmtime-versioned-export-macros",
]

[[package]]
name = "wasmtime-environ"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c25bfeaa16432d59a0706e2463d315ef4c9ebcfaf5605670b99d46373bdf9f27"
dependencies = [
 "anyhow",
 "cpp_demangle",
 "cranelift-bitset",
 "cranelift-entity",
 "gimli 0.31.1",
 "indexmap 2.9.0",
 "log",
 "object",
 "postcard",
 "rustc-demangle",
 "semver",
 "serde",
 "serde_derive",
 "smallvec",
 "target-lexicon 0.12.16",
 "wasm-encoder 0.219.2",
 "wasmparser 0.219.2",
 "wasmprinter",
 "wasmtime-component-util",
]

[[package]]
name = "wasmtime-fiber"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759ab0caa3821a6211743fe1eed448ab9df439e3af6c60dea15486c055611806"
dependencies = [
 "anyhow",
 "cc",
 "cfg-if",
 "rustix 0.38.44",
 "wasmtime-asm-macros",
 "wasmtime-versioned-export-macros",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-jit-debug"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab2a056056e9ac6916c2b8e4743408560300c1355e078c344211f13210d449b3"
dependencies = [
 "object",
 "rustix 0.38.44",
 "wasmtime-versioned-export-macros",
]

[[package]]
name = "wasmtime-jit-icache-coherence"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91b218a92866f74f35162f5d03a4e0f62cd0e1cc624285b1014275e5d4575fad"
dependencies = [
 "anyhow",
 "cfg-if",
 "libc",
 "windows-sys 0.59.0",
]

[[package]]
name = "wasmtime-slab"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d5f8acf677ee6b3b8ba400dd9753ea4769e56a95c4b30b045ac6d2d54b2f8ea"

[[package]]
name = "wasmtime-versioned-export-macros"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df09be00c38f49172ca9936998938476e3f2df782673a39ae2ef9fb0838341b6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "wasmtime-winch"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89d6b5297bea14d8387c3974b2b011de628cc9b188f135cec752b74fd368964b"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "gimli 0.31.1",
 "object",
 "target-lexicon 0.12.16",
 "wasmparser 0.219.2",
 "wasmtime-cranelift",
 "wasmtime-environ",
 "winch-codegen",
]

[[package]]
name = "wasmtime-wit-bindgen"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf3963c9c29df91564d8bd181eb00d0dbaeafa1b2a01e15952bb7391166b704e"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "indexmap 2.9.0",
 "wit-parser",
]

[[package]]
name = "wast"
version = "35.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ef140f1b49946586078353a453a1d28ba90adfc54dde75710bc1931de204d68"
dependencies = [
 "leb128",
]

[[package]]
name = "wast"
version = "230.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8edac03c5fa691551531533928443faf3dc61a44f814a235c7ec5d17b7b34f1"
dependencies = [
 "bumpalo",
 "leb128fmt",
 "memchr",
 "unicode-width 0.2.0",
 "wasm-encoder 0.230.0",
]

[[package]]
name = "wat"
version = "1.230.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d77d62229e38db83eac32bacb5f61ebb952366ab0dae90cf2b3c07a65eea894"
dependencies = [
 "wast 230.0.0",
]

[[package]]
name = "watcher"
version = "0.4.2"
source = "git+https://github.com/databendlabs/watcher?tag=v0.4.2#38e5109dd07a0fd496a48646dddd7de43242d75d"
dependencies = [
 "futures",
 "log",
 "span-map",
 "tokio",
 "tokio-util",
]

[[package]]
name = "web-sys"
version = "0.3.77"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33b6dd2ef9186f1f2072e409e99cd22a975331a6b3591b12c764e0e55c60d5d2"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "web-time"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a6580f308b1fad9207618087a65c04e7a10bc77e02c8e84e9b00dd4b12fa0bb"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki-roots"
version = "0.26.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "521bc38abb08001b01866da9f51eb7c5d647a19260e00054a8c7fd5f9e57f7a9"
dependencies = [
 "webpki-roots 1.0.0",
]

[[package]]
name = "webpki-roots"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2853738d1cc4f2da3a225c18ec6c3721abb31961096e9dbf5ab35fa88b19cfdb"
dependencies = [
 "rustls-pki-types",
]

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.44",
]

[[package]]
name = "whoami"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6994d13118ab492c3c80c1f81928718159254c53c472bf9ce36f8dae4add02a7"
dependencies = [
 "redox_syscall 0.5.12",
 "wasite",
]

[[package]]
name = "widestring"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd7cf3379ca1aac9eea11fba24fd7e315d621f8dfe35c8d7d2be8b793726e07d"

[[package]]
name = "wiggle"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80e0f6ef83a263c0fa11957c363aeaa76dc84832484d0e119f22810d4d0e09a7"
dependencies = [
 "anyhow",
 "async-trait",
 "bitflags 2.9.0",
 "thiserror 1.0.69",
 "tracing",
 "wasmtime",
 "wiggle-macro",
]

[[package]]
name = "wiggle-generate"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd266b290a0fdace3af6a05c6ebbcc54de303a774448ecf5a98cd0bc12d89c52"
dependencies = [
 "anyhow",
 "heck 0.5.0",
 "proc-macro2",
 "quote",
 "shellexpand",
 "syn 2.0.101",
 "witx",
]

[[package]]
name = "wiggle-macro"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b8eb1a5783540696c59cefbfc9e52570c2d5e62bd47bdf0bdcef29231879db2"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "wiggle-generate",
]

[[package]]
name = "wildmatch"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68ce1ab1f8c62655ebe1350f589c61e505cf94d385bc6a12899442d9081e71fd"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf221c93e13a30d793f7645a0e7762c55d169dbb0a49671918a2319d289b10bb"
dependencies = [
 "windows-sys 0.59.0",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "winch-codegen"
version = "27.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b42b678c8651ec4900d7600037d235429fc985c31cbc33515885ec0d2a9e158"
dependencies = [
 "anyhow",
 "cranelift-codegen",
 "gimli 0.31.1",
 "regalloc2",
 "smallvec",
 "target-lexicon 0.12.16",
 "wasmparser 0.219.2",
 "wasmtime-cranelift",
 "wasmtime-environ",
]

[[package]]
name = "windows"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e686886bc078bc1b0b600cac0147aadb815089b6e4da64016cbd754b6342700f"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12342cb4d8e3b046f3d80effd474a7a02447231330ef77d71daa6fbc40681143"
dependencies = [
 "windows-core 0.57.0",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows"
version = "0.61.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c5ee8f3d025738cb02bad7868bbb5f8a6327501e870bf51f1b455b0a2454a419"
dependencies = [
 "windows-collections",
 "windows-core 0.61.0",
 "windows-future",
 "windows-link",
 "windows-numerics",
]

[[package]]
name = "windows-collections"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3beeceb5e5cfd9eb1d76b381630e82c4241ccd0d27f1a39ed41b2760b255c5e8"
dependencies = [
 "windows-core 0.61.0",
]

[[package]]
name = "windows-core"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2ed2439a290666cd67ecce2b0ffaad89c2a56b976b736e6ece670297897832d"
dependencies = [
 "windows-implement 0.57.0",
 "windows-interface 0.57.0",
 "windows-result 0.1.2",
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-core"
version = "0.61.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4763c1de310c86d75a878046489e2e5ba02c649d185f21c67d4cf8a56d098980"
dependencies = [
 "windows-implement 0.60.0",
 "windows-interface 0.59.1",
 "windows-link",
 "windows-result 0.3.2",
 "windows-strings 0.4.0",
]

[[package]]
name = "windows-future"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a1d6bbefcb7b60acd19828e1bc965da6fcf18a7e39490c5f8be71e54a19ba32"
dependencies = [
 "windows-core 0.61.0",
 "windows-link",
]

[[package]]
name = "windows-implement"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9107ddc059d5b6fbfbffdfa7a7fe3e22a226def0b2608f72e9d552763d3e1ad7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-implement"
version = "0.60.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a47fddd13af08290e67f4acabf4b459f647552718f683a7b415d290ac744a836"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.57.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29bee4b38ea3cde66011baa44dba677c432a78593e202392d1e9070cf2a7fca7"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-interface"
version = "0.59.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd9211b69f8dcdfa817bfd14bf1c97c9188afa36f4750130fcdf3f400eca9fa8"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "windows-link"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76840935b766e1b0a05c0066835fb9ec80071d4c09a16f6bd5f7e655e3c14c38"

[[package]]
name = "windows-numerics"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9150af68066c4c5c07ddc0ce30421554771e528bde427614c61038bc2c92c2b1"
dependencies = [
 "windows-core 0.61.0",
 "windows-link",
]

[[package]]
name = "windows-registry"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4286ad90ddb45071efd1a66dfa43eb02dd0dfbae1545ad6cc3c51cf34d7e8ba3"
dependencies = [
 "windows-result 0.3.2",
 "windows-strings 0.3.1",
 "windows-targets 0.53.0",
]

[[package]]
name = "windows-result"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e383302e8ec8515204254685643de10811af0ed97ea37210dc26fb0032647f8"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-result"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c64fd11a4fd95df68efcfee5f44a294fe71b8bc6a91993e2791938abcc712252"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-strings"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87fa48cc5d406560701792be122a10132491cff9d0aeb23583cc2dcafc847319"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-strings"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a2ba9642430ee452d5a7aa78d72907ebe8cfda358e8cb7918a2050581322f97"
dependencies = [
 "windows-link",
]

[[package]]
name = "windows-sys"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a3e1820f08b8513f676f7ab6c1f99ff312fb97b553d30ff4dd86f9f15728aa7"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm 0.52.6",
 "windows_aarch64_msvc 0.52.6",
 "windows_i686_gnu 0.52.6",
 "windows_i686_gnullvm 0.52.6",
 "windows_i686_msvc 0.52.6",
 "windows_x86_64_gnu 0.52.6",
 "windows_x86_64_gnullvm 0.52.6",
 "windows_x86_64_msvc 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1e4c7e8ceaaf9cb7d7507c974735728ab453b67ef8f18febdd7c11fe59dca8b"
dependencies = [
 "windows_aarch64_gnullvm 0.53.0",
 "windows_aarch64_msvc 0.53.0",
 "windows_i686_gnu 0.53.0",
 "windows_i686_gnullvm 0.53.0",
 "windows_i686_msvc 0.53.0",
 "windows_x86_64_gnu 0.53.0",
 "windows_x86_64_gnullvm 0.53.0",
 "windows_x86_64_msvc 0.53.0",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b8d5f90ddd19cb4a147a5fa63ca848db3df085e25fee3cc10b39b6eebae764"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_aarch64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7651a1f62a11b8cbd5e0d42526e55f2c99886c77e007179efff86c2b137e66c"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1dc67659d35f387f5f6c479dc4e28f1d4bb90ddd1a5d3da2e5d97b42d6272c3"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce6ccbdedbf6d6354471319e781c0dfef054c81fbc7cf83f338a4296c0cae11"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_i686_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "581fee95406bb13382d2f65cd4a908ca7b1e4c2f1917f143ba16efe98a589b5d"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnu"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e55b5ac9ea33f2fc1716d1742db15574fd6fc8dadc51caab1c16a3d3b4190ba"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a6e035dd0599267ce1ee132e51c27dd29437f63325753051e71dd9e42406c57"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "windows_x86_64_msvc"
version = "0.53.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "271414315aff87387382ec3d271b52d7ae78726f5d44ac98b4f4030c91880486"

[[package]]
name = "winnow"
version = "0.5.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f593a95398737aeed53e489c785df13f3618e41dbcd6718c6addbf1395aa6876"
dependencies = [
 "memchr",
]

[[package]]
name = "winnow"
version = "0.6.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e90edd2ac1aa278a5c4599b1d89cf03074b610800f866d4026dc199d7929a28"
dependencies = [
 "memchr",
]

[[package]]
name = "winnow"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06928c8748d81b05c9be96aad92e1b6ff01833332f281e8cfca3be4b35fc9ec"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if",
 "windows-sys 0.48.0",
]

[[package]]
name = "winx"
version = "0.36.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f3fd376f71958b862e7afb20cfe5a22830e1963462f3a17f49d82a6c1d1f42d"
dependencies = [
 "bitflags 2.9.0",
 "windows-sys 0.59.0",
]

[[package]]
name = "wiremock"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "101681b74cd87b5899e87bcf5a64e83334dd313fcd3053ea72e6dba18928e301"
dependencies = [
 "assert-json-diff",
 "async-trait",
 "base64 0.22.1",
 "deadpool",
 "futures",
 "http 1.3.1",
 "http-body-util",
 "hyper 1.6.0",
 "hyper-util",
 "log",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "tokio",
 "url",
]

[[package]]
name = "wit-bindgen-rt"
version = "0.39.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f42320e61fe2cfd34354ecb597f86f413484a798ba44a8ca1165c58d42da6c1"
dependencies = [
 "bitflags 2.9.0",
]

[[package]]
name = "wit-parser"
version = "0.219.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca004bb251010fe956f4a5b9d4bf86b4e415064160dd6669569939e8cbf2504f"
dependencies = [
 "anyhow",
 "id-arena",
 "indexmap 2.9.0",
 "log",
 "semver",
 "serde",
 "serde_derive",
 "serde_json",
 "unicode-xid",
 "wasmparser 0.219.2",
]

[[package]]
name = "witx"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e366f27a5cabcddb2706a78296a40b8fcc451e1a6aba2fc1d94b4a01bdaaef4b"
dependencies = [
 "anyhow",
 "log",
 "thiserror 1.0.69",
 "wast 35.0.2",
]

[[package]]
name = "wkt"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54f7f1ff4ea4c18936d6cd26a6fd24f0003af37e951a8e0e8b9e9a2d0bd0a46d"
dependencies = [
 "geo-types",
 "log",
 "num-traits",
 "thiserror 1.0.69",
]

[[package]]
name = "writeable"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ea2f10b9bb0928dfb1b42b65e1f9e36f7f54dbdf08457afefb38afcdec4fa2bb"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "xattr"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d65cbf2f12c15564212d48f4e3dfb87923d25d611f2aed18f4cb23f0413d89e"
dependencies = [
 "libc",
 "rustix 1.0.7",
]

[[package]]
name = "xmlparser"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66fee0b777b0f5ac1c69bb06d361268faafa61cd4682ae064a171c16c433e9e4"

[[package]]
name = "xorf"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf24c008fe464f5d8f58b8d16a1ab7e930bd73b2a6933ff8704c414b2bed7f92"
dependencies = [
 "libm",
]

[[package]]
name = "xorfilter-rs"
version = "0.5.2"
source = "git+https://github.com/datafuse-extras/xorfilter?tag=databend-alpha.4#3bd8eb6b5b5e134703cc3b2807dfe43a763aa886"
dependencies = [
 "cbordata",
]

[[package]]
name = "xz2"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388c44dc09d76f1536602ead6d325eb532f5c122f17782bd57fb47baeeb767e2"
dependencies = [
 "lzma-sys",
]

[[package]]
name = "yansi"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfe53a6657fd280eaa890a3bc59152892ffa3e30101319d168b781ed6529b049"

[[package]]
name = "yoke"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f41bb01b8226ef4bfd589436a297c53d118f65921786300e427be8d487695cc"
dependencies = [
 "serde",
 "stable_deref_trait",
 "yoke-derive",
 "zerofrom",
]

[[package]]
name = "yoke-derive"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38da3c9736e16c5d3c8c597a9aaa5d1fa565d0532ae05e27c24aa62fb32c0ab6"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure",
]

[[package]]
name = "z85"
version = "3.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b3a41ce106832b4da1c065baa4c31cf640cf965fa1483816402b7f6b96f0a64"

[[package]]
name = "zerocopy"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1702d9583232ddb9174e01bb7c15a2ab8fb1bc6f227aa1233858c351a3ba0cb"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "28a6e20d751156648aa063f3800b706ee209a32c0b4d9f24be3d980b01be55ef"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerofrom"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "50cc42e0333e05660c3587f3bf9d0478688e15d870fab3346451ce7f8c9fbea5"
dependencies = [
 "zerofrom-derive",
]

[[package]]
name = "zerofrom-derive"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d71e5d6e06ab090c67b5e44993ec16b72dcbaabc526db883a360057678b48502"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
 "synstructure",
]

[[package]]
name = "zeroize"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ced3678a2879b30306d323f4542626697a464a97c0a07c9aebf7ebca65cd4dde"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zerotrie"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36f0bbd478583f79edad978b407914f61b2972f5af6fa089686016be8f9af595"
dependencies = [
 "displaydoc",
 "yoke",
 "zerofrom",
]

[[package]]
name = "zerovec"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a05eb080e015ba39cc9e23bbe5e7fb04d5fb040350f99f34e338d5fdd294428"
dependencies = [
 "yoke",
 "zerofrom",
 "zerovec-derive",
]

[[package]]
name = "zerovec-derive"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b96237efa0c878c64bd89c436f661be4e46b2f3eff1ebb976f7ef2321d2f58f"
dependencies = [
 "proc-macro2",
 "quote",
 "syn 2.0.101",
]

[[package]]
name = "zip"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12598812502ed0105f607f941c386f43d441e00148fce9dec3ca5ffb0bde9308"
dependencies = [
 "aes",
 "arbitrary",
 "bzip2 0.5.2",
 "constant_time_eq",
 "crc32fast",
 "deflate64",
 "flate2",
 "getrandom 0.3.3",
 "hmac",
 "indexmap 2.9.0",
 "lzma-rs",
 "memchr",
 "pbkdf2",
 "sha1",
 "time",
 "xz2",
 "zeroize",
 "zopfli",
 "zstd 0.13.3",
]

[[package]]
name = "zlib-rs"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "868b928d7949e09af2f6086dfc1e01936064cc7a819253bce650d4e2a2d63ba8"

[[package]]
name = "zopfli"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "edfc5ee405f504cd4984ecc6f14d02d55cfda60fa4b689434ef4102aae150cd7"
dependencies = [
 "bumpalo",
 "crc32fast",
 "log",
 "simd-adler32",
]

[[package]]
name = "zstd"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a27595e173641171fc74a1232b7b1c7a7cb6e18222c11e9dfb9888fa424c53c"
dependencies = [
 "zstd-safe 6.0.6",
]

[[package]]
name = "zstd"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e91ee311a569c327171651566e07972200e76fcfe2242a4fa446149a3881c08a"
dependencies = [
 "zstd-safe 7.2.4",
]

[[package]]
name = "zstd-safe"
version = "6.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee98ffd0b48ee95e6c5168188e44a54550b1564d9d530ee21d5f0eaed1069581"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "7.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f49c4d5f0abb602a93fb8736af2a4f4dd9512e36f7f570d66e65ff867ed3b9d"
dependencies = [
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.15+zstd.1.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb81183ddd97d0c74cedf1d50d85c8d08c1b8b68ee863bdee9e706eedba1a237"
dependencies = [
 "cc",
 "pkg-config",
]
