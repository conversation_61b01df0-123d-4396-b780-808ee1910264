// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::sync::Arc;

use bounds::Bounds;
use databend_common_expression::local_block_meta_serde;
use databend_common_expression::BlockMetaInfo;
use databend_common_expression::BlockMetaInfoDowncast;
use databend_common_expression::DataBlock;
use databend_common_expression::DataSchemaRef;
use databend_common_pipeline_transforms::SortSpillParams;
use sort_spill::SpillableBlock;

use crate::spillers::Spiller;

mod bounds;
mod merge_sort;
mod sort_builder;
mod sort_collect;
mod sort_combine;
mod sort_exchange;
mod sort_exchange_injector;
mod sort_merge_stream;
mod sort_restore;
mod sort_route;
mod sort_shuffle;
mod sort_spill;

pub use merge_sort::*;
pub use sort_builder::*;
pub use sort_collect::*;
pub use sort_combine::*;
pub use sort_exchange::*;
pub use sort_exchange_injector::*;
pub use sort_merge_stream::*;
pub use sort_restore::*;
pub use sort_route::*;
pub use sort_shuffle::*;

#[derive(Clone)]
struct Base {
    schema: DataSchemaRef,
    spiller: Arc<Spiller>,
    sort_row_offset: usize,
    limit: Option<usize>,
}

#[derive(Debug)]
struct SortCollectedMeta {
    params: SortSpillParams,
    bounds: Bounds,
    sequences: Vec<Box<[SpillableBlock]>>,
}

local_block_meta_serde!(SortCollectedMeta);

#[typetag::serde(name = "sort_collected")]
impl BlockMetaInfo for SortCollectedMeta {}

#[derive(Debug)]
struct SortScatteredMeta(pub Vec<Option<SortCollectedMeta>>);

local_block_meta_serde!(SortScatteredMeta);

#[typetag::serde(name = "sort_scattered")]
impl BlockMetaInfo for SortScatteredMeta {}

trait MemoryRows {
    fn in_memory_rows(&self) -> usize;
}

impl MemoryRows for Vec<DataBlock> {
    fn in_memory_rows(&self) -> usize {
        self.iter().map(|s| s.num_rows()).sum::<usize>()
    }
}

#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
struct SortExchangeMeta {
    params: SortSpillParams,
    bounds: Bounds,
}

#[typetag::serde(name = "sort_exchange")]
impl BlockMetaInfo for SortExchangeMeta {
    fn equals(&self, info: &Box<dyn BlockMetaInfo>) -> bool {
        SortExchangeMeta::downcast_ref_from(info).is_some_and(|other| self == other)
    }

    fn clone_self(&self) -> Box<dyn BlockMetaInfo> {
        Box::new(self.clone())
    }
}

#[derive(Debug, Clone, PartialEq, Eq, serde::Serialize, serde::Deserialize)]
pub struct SortBound {
    bound_index: u32,
    more: bool,
}

impl SortBound {
    fn create(bound_index: u32, more: bool) -> Box<dyn BlockMetaInfo> {
        debug_assert!(bound_index != u32::MAX);
        SortBound { bound_index, more }.boxed()
    }
}

#[typetag::serde(name = "sort_bound")]
impl BlockMetaInfo for SortBound {
    fn equals(&self, info: &Box<dyn BlockMetaInfo>) -> bool {
        SortBound::downcast_ref_from(info).is_some_and(|other| self == other)
    }

    fn clone_self(&self) -> Box<dyn BlockMetaInfo> {
        Box::new(self.clone())
    }
}
