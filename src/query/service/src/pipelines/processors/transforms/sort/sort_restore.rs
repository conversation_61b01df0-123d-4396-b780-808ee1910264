// Copyright 2021 Datafuse Labs
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

use std::any::Any;
use std::collections::VecDeque;
use std::sync::Arc;

use anyhow::Ok;
use databend_common_exception::Result;
use databend_common_expression::BlockMetaInfoDowncast;
use databend_common_expression::DataBlock;
use databend_common_pipeline_core::processors::Event;
use databend_common_pipeline_core::processors::InputPort;
use databend_common_pipeline_core::processors::OutputPort;
use databend_common_pipeline_transforms::processors::sort::algorithm::SortAlgorithm;
use databend_common_pipeline_transforms::AccumulatingTransform;
use databend_common_pipeline_transforms::BlockingTransform;
use databend_common_pipeline_transforms::BlockingTransformer;
use databend_common_pipeline_transforms::HookTransform;
use databend_common_pipeline_transforms::HookTransformer;

use super::sort_spill::SortSpill;
use super::Base;
use super::SortBound;
use super::SortCollectedMeta;
use crate::pipelines::processors::transforms::sort::sort_spill::OutputData;

pub struct TransformSortRestore<A: SortAlgorithm> {
    input: Vec<SortCollectedMeta>,
    output: Option<DataBlock>,

    /// If the next transform of current transform is [`super::transform_multi_sort_merge::MultiSortMergeProcessor`],
    /// we can generate and output the order column to avoid the extra converting in the next transform.
    remove_order_col: bool,

    base: Base,
    inner: Option<SortSpill<A>>,
}

impl<A> TransformSortRestore<A>
where A: SortAlgorithm + Send + 'static
{
    pub(super) fn create(
        input: Arc<InputPort>,
        output: Arc<OutputPort>,
        base: Base,
        output_order_col: bool,
    ) -> Result<HookTransformer<Self>> {
        Ok(HookTransformer::new(input, output, Self {
            input: Vec::new(),
            output: None,
            remove_order_col: !output_order_col,
            base,
            inner: None,
        }))
    }
}

#[async_trait::async_trait]
impl<A> HookTransform for TransformSortRestore<A>
where
    A: SortAlgorithm + 'static,
    A::Rows: 'static,
{
    const NAME: &'static str = "TransformSortRestore";

    fn on_input(&mut self, mut block: DataBlock) -> Result<()> {
        assert!(self.inner.is_none());
        let meta = block
            .take_meta()
            .and_then(SortCollectedMeta::downcast_from)
            .expect("require a SortCollectedMeta");
        self.input.push(meta);
        Ok(())
    }

    fn on_output(&mut self) -> Result<Option<DataBlock>> {
        Ok(self.output.take())
    }

    fn need_process(&self, input_finished: bool) -> Option<Event> {
        if input_finished && (self.inner.is_some() || !self.input.is_empty()) {
            Some(Event::Async)
        } else {
            None
        }
    }

    #[async_backtrace::framed]
    async fn async_process(&mut self) -> Result<()> {
        let spill_sort = match &mut self.inner {
            Some(inner) => inner,
            None => {
                debug_assert!(!self.input.is_empty());
                let sequences = self
                    .input
                    .iter_mut()
                    .flat_map(|meta| meta.sequences.drain(..))
                    .collect();

                let meta = self.input.pop().unwrap();
                self.input.clear();
                self.inner
                    .insert(SortSpill::from_meta(self.base.clone(), SortCollectedMeta {
                        sequences,
                        ..meta
                    }))
            }
        };

        let OutputData {
            block,
            bound: (bound_index, _),
            finish,
        } = spill_sort.on_restore().await?;
        if let Some(block) = block {
            let mut block = block.add_meta(Some(SortBound::create(bound_index, true)))?;
            if self.remove_order_col {
                block.pop_columns(1);
            }
            self.output = Some(block);
        }
        if finish {
            self.inner = None;
        }
        Ok(())
    }
}

struct SortBoundEdge {
    input: Arc<InputPort>,
    output: Arc<OutputPort>,
    data: VecDeque<DataBlock>,
}

impl SortBoundEdge {
    fn process(&mut self) -> Result<Event> {
        if self.data.is_empty() {
            if self.input.is_finished() {
                self.output.finish();
                return Ok(Event::Finished);
            }
            let Some(block) = self.input.pull_data()? else {
                self.input.set_need_data();
                return Ok(Event::NeedData);
            };
            self.data.push_back(block);
        }

        if self.data.len() == 1 {
            if self.input.is_finished() {
                let front = self.data.pop_front().unwrap();
                let mut meta = front
                    .take_meta()
                    .and_then(SortBound::downcast_from)
                    .expect("require a SortBound");
                meta.more = false;
                front.add_meta(Some(meta.boxed()))?;
                self.output.push_data(Some(front));
                self.output.finish();
                return Ok(Event::Finished);
            }
            let Some(block) = self.input.pull_data()? else {
                self.input.set_need_data();
                return Ok(Event::NeedData);
            };
            self.data.push_back(block);
        }

        debug_assert_eq!(self.data.len(), 2);

        let front = self.data.front_mut().unwrap();
        let front_meta = front
            .get_meta()
            .and_then(SortBound::downcast_ref_from)
            .expect("require a SortBound");
        let back_meta = self
            .data
            .back()
            .unwrap()
            .get_meta()
            .and_then(SortBound::downcast_ref_from)
            .expect("require a SortBound");

        if front_meta.bound_index != back_meta.bound_index {
            let meta = SortBound::create(front_meta.bound_index, false);
            front.replace_meta(meta);
        }

        self.output.push_data(self.data.pop_front());
        Ok(Event:)
    }
}

impl Processor for SortBoundEdge {
    fn name(&self) -> String {
        String::from("SortBoundEdgeTransform")
    }

    fn as_any(&mut self) -> &mut dyn Any {
        self
    }

    fn event(&mut self) -> Result<Event> {
        if self.output.is_finished() {
            self.input.finish();
            return Ok(Event::Finished);
        }

        if !self.output.can_push() {
            self.input.set_not_need_data();
            return Ok(Event::NeedConsume);
        }

        if let Some(output) = self.inner.on_output()? {
            self.output.push_data(Ok(output));
            return Ok(Event::NeedConsume);
        }

        if let Some(event) = self.inner.need_process(self.input.is_finished()) {
            return Ok(event);
        }

        if self.input.has_data() {
            let data = self.input.pull_data().unwrap()?;
            self.data.push_back(data);
        }

        if let Some(event) = self.inner.need_process(self.input.is_finished()) {
            return Ok(event);
        }

        if self.input.is_finished() {
            self.output.finish();
            Ok(Event::Finished)
        } else {
            self.input.set_need_data();
            Ok(Event::NeedData)
        }
    }

    fn process(&mut self) -> Result<()> {
        self.inner.process()
    }

    async fn async_process(&mut self) -> Result<()> {
        self.inner.async_process().await
    }
}
