[package]
name = "databend-query"
description = "A real-time Cloud Distributed Query Engine"
version = { workspace = true }
authors = { workspace = true }
license = { workspace = true }
publish = { workspace = true }
edition = { workspace = true }

[features]
default = ["simd"]
simd = ["databend-common-column/simd"]
python-udf = ["arrow-udf-runtime/python"]
disable_initial_exec_tls = ["databend-common-base/disable_initial_exec_tls"]
jemalloc = ["databend-common-storages-system/jemalloc"]
memory-profiling = ["databend-common-base/memory-profiling", "databend-common-http/memory-profiling"]
storage-hdfs = ["opendal/services-hdfs", "databend-common-storage/storage-hdfs"]
io-uring = [
    "databend-common-meta-store/io-uring",
]

[dependencies]
anyhow = { workspace = true }
arrow-array = { workspace = true }
arrow-buffer = { workspace = true }
arrow-flight = { workspace = true }
arrow-ipc = { workspace = true, features = ["lz4", "zstd"] }
arrow-schema = { workspace = true }
arrow-select = { workspace = true }
arrow-udf-runtime = { workspace = true, features = ["javascript", "wasm"] }
async-backtrace = { workspace = true }
async-channel = { workspace = true }
async-compat = { workspace = true }
async-recursion = { workspace = true }
async-stream = { workspace = true }
async-trait = { workspace = true }
backoff = { workspace = true, features = ["futures", "tokio"] }
backon = { workspace = true }
base64 = { workspace = true }
buf-list = { workspace = true }
bumpalo = { workspace = true }
byteorder = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
chrono-tz = { workspace = true }
concurrent-queue = { workspace = true }
ctor = { workspace = true }
dashmap = { workspace = true }
databend-common-ast = { workspace = true }
databend-common-base = { workspace = true }
databend-common-cache = { workspace = true }
databend-common-catalog = { workspace = true }
databend-common-cloud-control = { workspace = true }
databend-common-column = { workspace = true }
databend-common-config = { workspace = true }
databend-common-exception = { workspace = true }
databend-common-expression = { workspace = true }
databend-common-formats = { workspace = true }
databend-common-functions = { workspace = true }
databend-common-grpc = { workspace = true }
databend-common-hashtable = { workspace = true }
databend-common-http = { workspace = true }
databend-common-io = { workspace = true }
databend-common-license = { workspace = true }
databend-common-management = { workspace = true }
databend-common-meta-api = { workspace = true }
databend-common-meta-app = { workspace = true }
databend-common-meta-app-types = { workspace = true }
databend-common-meta-client = { workspace = true }
databend-common-meta-kvapi = { workspace = true }
databend-common-meta-semaphore = { workspace = true }
databend-common-meta-store = { workspace = true }
databend-common-meta-types = { workspace = true }
databend-common-metrics = { workspace = true }
databend-common-openai = { workspace = true }
databend-common-pipeline-core = { workspace = true }
databend-common-pipeline-sinks = { workspace = true }
databend-common-pipeline-sources = { workspace = true }
databend-common-pipeline-transforms = { workspace = true }
databend-common-script = { workspace = true }
databend-common-settings = { workspace = true }
databend-common-sql = { workspace = true }
databend-common-storage = { workspace = true }
databend-common-storages-delta = { workspace = true }
databend-common-storages-factory = { workspace = true }
databend-common-storages-fuse = { workspace = true }
databend-common-storages-hive = { workspace = true }
databend-common-storages-iceberg = { workspace = true }
databend-common-storages-information-schema = { workspace = true }
databend-common-storages-memory = { workspace = true }
databend-common-storages-null = { workspace = true }
databend-common-storages-orc = { workspace = true }
databend-common-storages-parquet = { workspace = true }
databend-common-storages-result-cache = { workspace = true }
databend-common-storages-stage = { workspace = true }
databend-common-storages-stream = { workspace = true }
databend-common-storages-system = { workspace = true }
databend-common-storages-view = { workspace = true }
databend-common-tracing = { workspace = true }
databend-common-users = { workspace = true }
databend-common-version = { workspace = true }
databend-enterprise-aggregating-index = { workspace = true }
databend-enterprise-attach-table = { workspace = true }
databend-enterprise-data-mask-feature = { workspace = true }
databend-enterprise-hilbert-clustering = { workspace = true }
databend-enterprise-resources-management = { workspace = true }
databend-enterprise-stream-handler = { workspace = true }
databend-enterprise-table-index = { workspace = true }
databend-enterprise-vacuum-handler = { workspace = true }
databend-enterprise-virtual-column = { workspace = true }
databend-storages-common-blocks = { workspace = true }
databend-storages-common-cache = { workspace = true }
databend-storages-common-index = { workspace = true }
databend-storages-common-io = { workspace = true }
databend-storages-common-session = { workspace = true }
databend-storages-common-stage = { workspace = true }
databend-storages-common-table-meta = { workspace = true }
derive-visitor = { workspace = true }
ethnum = { workspace = true }
fastrace = { workspace = true }
flatbuffers = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }
geozero = { workspace = true }
headers = { workspace = true }
hex = { workspace = true }
http = { workspace = true }
humantime = { workspace = true }
indicatif = { workspace = true }
itertools = { workspace = true }
jiff = { workspace = true }
jsonb = { workspace = true }
jwt-simple = { workspace = true }
log = { workspace = true }
lz4 = { workspace = true }
match-template = { workspace = true }
md-5 = { workspace = true }
naive-cityhash = { workspace = true }
num_cpus = { workspace = true }
opendal = { workspace = true }
opensrv-mysql = { workspace = true }
opentelemetry = { workspace = true }
opentelemetry_sdk = { workspace = true }
parking_lot = { workspace = true }
parquet = { workspace = true }
paste = { workspace = true }
petgraph = { workspace = true }
pin-project-lite = { workspace = true }
poem = { workspace = true }
prometheus-client = { workspace = true }
prost = { workspace = true }
rand = { workspace = true }
recursive = { workspace = true }
redis = { workspace = true }
regex = { workspace = true }
reqwest = { workspace = true }
rustls = { workspace = true }
rustls-pemfile = { workspace = true }
rustls-pki-types = { workspace = true }
rustyline = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_stacker = { workspace = true }
serde_urlencoded = { workspace = true }
sha2 = { workspace = true }
socket2 = { workspace = true }
sqlx = { workspace = true }
sysinfo = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true }
tokio-stream = { workspace = true, features = ["net"] }
toml = { workspace = true, features = ["parse"] }
tonic = { workspace = true }
typetag = { workspace = true }
url = { workspace = true }
uuid = { workspace = true }
walkdir = { workspace = true }
xorf = { workspace = true }

[dev-dependencies]
arrow-cast = { workspace = true }
goldenfile = { workspace = true }
hex = { workspace = true }
hyper-util = { workspace = true }
jwt-simple = { workspace = true }
maplit = { workspace = true }
mysql_async = { workspace = true }
p256 = { workspace = true }
pretty_assertions = { workspace = true }
reqwest = { workspace = true }
serde_json.workspace = true
serde_yaml = { workspace = true }
temp-env = { workspace = true }
tempfile = { workspace = true }
tower = { workspace = true }
url = { workspace = true }
wiremock = { workspace = true }

[build-dependencies]
databend-common-building = { workspace = true }

[lints]
workspace = true

[package.metadata.cargo-machete]
ignored = ["match-template", "md-5"]
